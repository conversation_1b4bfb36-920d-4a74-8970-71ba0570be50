{"version": 3, "file": "record.js", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/record.ts"], "names": [], "mappings": ";;AAiBA,wCAuDC;AAxED,6BAAiF;AACjF,6CAAwD;AAIxD,wCAAiE;AAYjE,SAAgB,cAAc,CAC5B,GAAqD,EACrD,IAAU;IAEV,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,KAAK,2BAAqB,CAAC,OAAO,EAAE,CAAC;QAC/F,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;YACjC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CACxC,CAAC,GAAoC,EAAE,GAAW,EAAE,EAAE,CAAC,CAAC;gBACtD,GAAG,GAAG;gBACN,CAAC,GAAG,CAAC,EACH,IAAA,mBAAQ,EAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;oBAC3B,GAAG,IAAI;oBACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,EAAE,GAAG,CAAC;iBACtD,CAAC,IAAI,EAAE;aACX,CAAC,EACF,EAAE,CACH;YACD,oBAAoB,EAAE,KAAK;SACW,CAAC;IAC3C,CAAC;IAED,MAAM,MAAM,GAA0B;QACpC,IAAI,EAAE,QAAQ;QACd,oBAAoB,EAClB,IAAA,mBAAQ,EAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE;YAC3B,GAAG,IAAI;YACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,sBAAsB,CAAC;SAC3D,CAAC,IAAI,EAAE;KACX,CAAC;IAEF,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,KAAK,2BAAqB,CAAC,SAAS,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QACtG,MAAM,OAAO,GAAuC,MAAM,CAAC,OAAO,CAChE,IAAA,uBAAc,EAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CACvC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAEvF,OAAO;YACL,GAAG,MAAM;YACT,aAAa,EAAE,OAAO;SACvB,CAAC;IACJ,CAAC;SAAM,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,KAAK,2BAAqB,CAAC,OAAO,EAAE,CAAC;QACxE,OAAO;YACL,GAAG,MAAM;YACT,aAAa,EAAE;gBACb,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;aAC9B;SACF,CAAC;IACJ,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}