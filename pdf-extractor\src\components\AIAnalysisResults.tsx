'use client';

import React, { useState } from 'react';
import { AIAnalysis, QuotationDetails } from '@/types/pdf';
import { 
  Brain, 
  FileText, 
  DollarSign, 
  Calendar, 
  Building, 
  User, 
  Mail, 
  Phone, 
  MapPin,
  Package,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Copy,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface AIAnalysisResultsProps {
  analysis: AIAnalysis;
}

export default function AIAnalysisResults({ analysis }: AIAnalysisResultsProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    quotationDetails: true,
    insights: true,
    entities: false,
    recommendations: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case 'quotation': return 'bg-blue-100 text-blue-800';
      case 'invoice': return 'bg-green-100 text-green-800';
      case 'contract': return 'bg-purple-100 text-purple-800';
      case 'proposal': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-purple-600 p-2 rounded-lg">
            <Brain className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">AI Analysis Results</h3>
            <p className="text-gray-600">Intelligent document analysis powered by OpenAI</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-gray-500" />
            <span className="text-sm text-gray-600">Document Type:</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDocumentTypeColor(analysis.documentType)}`}>
              {analysis.documentType.charAt(0).toUpperCase() + analysis.documentType.slice(1)}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5 text-gray-500" />
            <span className="text-sm text-gray-600">Confidence:</span>
            <span className={`font-medium ${getConfidenceColor(analysis.confidence)}`}>
              {(analysis.confidence * 100).toFixed(1)}%
            </span>
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-white rounded-lg border p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-3">Summary</h4>
        <p className="text-gray-700">{analysis.summary}</p>
      </div>

      {/* Quotation Details */}
      {analysis.quotationDetails && (
        <div className="bg-white rounded-lg border">
          <button
            onClick={() => toggleSection('quotationDetails')}
            className="w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors"
          >
            <h4 className="text-lg font-semibold text-gray-900">Quotation Details</h4>
            {expandedSections.quotationDetails ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
          </button>
          {expandedSections.quotationDetails && (
            <div className="px-6 pb-6 border-t">
              <QuotationDetailsView details={analysis.quotationDetails} />
            </div>
          )}
        </div>
      )}

      {/* Key Insights */}
      <div className="bg-white rounded-lg border">
        <button
          onClick={() => toggleSection('insights')}
          className="w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors"
        >
          <h4 className="text-lg font-semibold text-gray-900">Key Insights</h4>
          {expandedSections.insights ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
        </button>
        {expandedSections.insights && (
          <div className="px-6 pb-6 border-t">
            <div className="space-y-3">
              {analysis.keyInsights.map((insight, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">{insight}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Recommendations */}
      {analysis.recommendations && analysis.recommendations.length > 0 && (
        <div className="bg-white rounded-lg border">
          <button
            onClick={() => toggleSection('recommendations')}
            className="w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors"
          >
            <h4 className="text-lg font-semibold text-gray-900">Recommendations</h4>
            {expandedSections.recommendations ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
          </button>
          {expandedSections.recommendations && (
            <div className="px-6 pb-6 border-t">
              <div className="space-y-3">
                {analysis.recommendations.map((recommendation, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{recommendation}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Extracted Entities */}
      <div className="bg-white rounded-lg border">
        <button
          onClick={() => toggleSection('entities')}
          className="w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors"
        >
          <h4 className="text-lg font-semibold text-gray-900">Extracted Entities</h4>
          {expandedSections.entities ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
        </button>
        {expandedSections.entities && (
          <div className="px-6 pb-6 border-t">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <EntityList title="Dates" items={analysis.extractedEntities.dates} icon={Calendar} />
              <EntityList title="Amounts" items={analysis.extractedEntities.amounts} icon={DollarSign} />
              <EntityList title="Companies" items={analysis.extractedEntities.companies} icon={Building} />
              <EntityList title="Contacts" items={analysis.extractedEntities.contacts} icon={Mail} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper Components
function QuotationDetailsView({ details }: { details: QuotationDetails }) {
  return (
    <div className="space-y-6 mt-4">
      {/* Basic Info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {details.quotationNumber && (
          <InfoItem label="Quote Number" value={details.quotationNumber} icon={FileText} />
        )}
        {details.date && (
          <InfoItem label="Date" value={details.date} icon={Calendar} />
        )}
        {details.validUntil && (
          <InfoItem label="Valid Until" value={details.validUntil} icon={Calendar} />
        )}
      </div>

      {/* Vendor & Customer */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {details.vendor && <ContactCard title="Vendor" contact={details.vendor} />}
        {details.customer && <ContactCard title="Customer" contact={details.customer} />}
      </div>

      {/* Items */}
      {details.items && details.items.length > 0 && (
        <div>
          <h5 className="text-md font-semibold text-gray-900 mb-3">Line Items</h5>
          <div className="overflow-x-auto">
            <table className="min-w-full border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Description</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-700">Qty</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-700">Unit Price</th>
                  <th className="px-4 py-2 text-right text-sm font-medium text-gray-700">Total</th>
                </tr>
              </thead>
              <tbody>
                {details.items.map((item, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-4 py-2 text-sm text-gray-900">{item.description}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right">{item.quantity} {item.unit}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right">{item.unitPrice}</td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right font-medium">{item.totalPrice}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Totals */}
      {details.totals && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="text-md font-semibold text-gray-900 mb-3">Totals</h5>
          <div className="space-y-2">
            {details.totals.subtotal && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Subtotal:</span>
                <span className="text-sm font-medium">{details.totals.subtotal} {details.totals.currency}</span>
              </div>
            )}
            {details.totals.tax && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Tax:</span>
                <span className="text-sm font-medium">{details.totals.tax} {details.totals.currency}</span>
              </div>
            )}
            {details.totals.discount && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Discount:</span>
                <span className="text-sm font-medium">-{details.totals.discount} {details.totals.currency}</span>
              </div>
            )}
            {details.totals.total && (
              <div className="flex justify-between border-t pt-2">
                <span className="text-lg font-semibold text-gray-900">Total:</span>
                <span className="text-lg font-bold text-green-600">{details.totals.total} {details.totals.currency}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Terms & Notes */}
      {details.terms && details.terms.length > 0 && (
        <div>
          <h5 className="text-md font-semibold text-gray-900 mb-3">Terms & Conditions</h5>
          <ul className="space-y-1">
            {details.terms.map((term, index) => (
              <li key={index} className="text-sm text-gray-700">• {term}</li>
            ))}
          </ul>
        </div>
      )}

      {details.notes && (
        <div>
          <h5 className="text-md font-semibold text-gray-900 mb-3">Notes</h5>
          <p className="text-sm text-gray-700">{details.notes}</p>
        </div>
      )}
    </div>
  );
}

function ContactCard({ title, contact }: { title: string, contact: any }) {
  return (
    <div className="border rounded-lg p-4">
      <h5 className="text-md font-semibold text-gray-900 mb-3">{title}</h5>
      <div className="space-y-2">
        {contact.name && (
          <div className="flex items-center space-x-2">
            <User className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-700">{contact.name}</span>
          </div>
        )}
        {contact.email && (
          <div className="flex items-center space-x-2">
            <Mail className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-700">{contact.email}</span>
          </div>
        )}
        {contact.phone && (
          <div className="flex items-center space-x-2">
            <Phone className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-700">{contact.phone}</span>
          </div>
        )}
        {contact.address && (
          <div className="flex items-start space-x-2">
            <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
            <span className="text-sm text-gray-700">{contact.address}</span>
          </div>
        )}
      </div>
    </div>
  );
}

function InfoItem({ label, value, icon: Icon }: { label: string, value: string, icon: any }) {
  return (
    <div className="flex items-center space-x-2">
      <Icon className="h-4 w-4 text-gray-500" />
      <div>
        <p className="text-xs text-gray-500">{label}</p>
        <p className="text-sm font-medium text-gray-900">{value}</p>
      </div>
    </div>
  );
}

function EntityList({ title, items, icon: Icon }: { title: string, items: string[], icon: any }) {
  if (items.length === 0) return null;

  return (
    <div>
      <div className="flex items-center space-x-2 mb-2">
        <Icon className="h-4 w-4 text-gray-500" />
        <h6 className="text-sm font-medium text-gray-900">{title}</h6>
      </div>
      <div className="space-y-1">
        {items.slice(0, 5).map((item, index) => (
          <div key={index} className="text-sm text-gray-700 bg-gray-50 px-2 py-1 rounded">
            {item}
          </div>
        ))}
        {items.length > 5 && (
          <div className="text-xs text-gray-500">
            +{items.length - 5} more items
          </div>
        )}
      </div>
    </div>
  );
}
