{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/pdfComparator.ts"], "sourcesContent": ["import OpenAI from 'openai';\nimport { ExtractedPDFData, PDFComparison, ComparisonDifference, ComparisonAnalysis, SideBySideComparison, QuotationScore } from '@/types/pdf';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\nexport class PDFComparator {\n  static async comparePDFs(documents: ExtractedPDFData[]): Promise<PDFComparison> {\n    try {\n      if (documents.length < 2) {\n        throw new Error('At least 2 documents are required for comparison');\n      }\n\n      const comparisonAnalysis = await this.performAIComparison(documents);\n\n      return {\n        id: this.generateId(),\n        documents,\n        differences: comparisonAnalysis.differences,\n        summary: comparisonAnalysis.summary,\n        recommendations: comparisonAnalysis.recommendations,\n        comparedAt: new Date().toISOString(),\n        comparisonType: this.detectComparisonType(documents)\n      };\n    } catch (error) {\n      console.error('PDF comparison error:', error);\n      throw error;\n    }\n  }\n\n  private static async performAIComparison(documents: ExtractedPDFData[]): Promise<{\n    differences: ComparisonDifference[];\n    summary: string;\n    recommendations: string[];\n  }> {\n    try {\n      const prompt = this.createComparisonPrompt(documents);\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4o-mini\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are an expert document comparison analyst specializing in business documents, quotations, contracts, and proposals. Compare the provided documents and identify key differences, focusing on pricing, terms, vendors, timelines, and other critical business factors.\"\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        temperature: 0.1,\n        max_tokens: 3000,\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('No response from OpenAI');\n      }\n\n      const analysis = JSON.parse(response);\n      return this.validateComparisonAnalysis(analysis);\n    } catch (error) {\n      console.error('AI comparison error:', error);\n      return this.createFallbackComparison(documents);\n    }\n  }\n\n  private static createComparisonPrompt(documents: ExtractedPDFData[]): string {\n    const documentSummaries = documents.map((doc, index) => {\n      const quotationDetails = doc.aiAnalysis?.quotationDetails;\n      return `\nDocument ${index + 1}: ${doc.fileName}\n- Document Type: ${doc.aiAnalysis?.documentType || 'unknown'}\n- Content Preview: ${doc.content.fullText.substring(0, 1000)}...\n- Quotation Details: ${quotationDetails ? JSON.stringify(quotationDetails, null, 2) : 'None extracted'}\n- Key Insights: ${doc.aiAnalysis?.keyInsights?.join('; ') || 'None'}\n`;\n    }).join('\\n');\n\n    return `\nCompare the following ${documents.length} business documents and provide a detailed analysis in JSON format.\n\n${documentSummaries}\n\nPlease provide analysis in this exact JSON structure:\n{\n  \"differences\": [\n    {\n      \"field\": \"pricing|vendor|terms|timeline|other\",\n      \"type\": \"added|removed|changed|same\",\n      \"oldValue\": \"value from first document or null\",\n      \"newValue\": \"value from second document or null\", \n      \"significance\": \"high|medium|low\",\n      \"description\": \"Clear description of the difference\"\n    }\n  ],\n  \"summary\": \"Overall summary of key differences between documents\",\n  \"recommendations\": [\n    \"Actionable recommendations based on the comparison\"\n  ]\n}\n\nFocus on comparing:\n1. PRICING: Total costs, line item prices, discounts, taxes\n2. VENDORS: Company names, contact information, credentials\n3. TERMS: Payment terms, delivery dates, warranties, conditions\n4. TIMELINE: Project duration, milestones, deadlines\n5. SCOPE: Services/products included, specifications, quantities\n6. RISKS: Potential issues, missing information, unclear terms\n\nFor each difference, assess:\n- Business impact (high/medium/low significance)\n- Financial implications\n- Risk factors\n- Recommendations for decision making\n\nReturn only valid JSON without markdown formatting.\n`;\n  }\n\n  private static validateComparisonAnalysis(analysis: any): {\n    differences: ComparisonDifference[];\n    summary: string;\n    recommendations: string[];\n  } {\n    return {\n      differences: Array.isArray(analysis.differences)\n        ? analysis.differences.map((diff: any) => ({\n          field: diff.field || 'other',\n          type: diff.type || 'changed',\n          oldValue: diff.oldValue,\n          newValue: diff.newValue,\n          significance: diff.significance || 'medium',\n          description: diff.description || 'No description available'\n        }))\n        : [],\n      summary: analysis.summary || 'Comparison completed',\n      recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : []\n    };\n  }\n\n  private static createFallbackComparison(documents: ExtractedPDFData[]): {\n    differences: ComparisonDifference[];\n    summary: string;\n    recommendations: string[];\n  } {\n    const differences: ComparisonDifference[] = [];\n\n    // Basic comparison of document types\n    const docTypes = documents.map(d => d.aiAnalysis?.documentType || 'unknown');\n    if (new Set(docTypes).size > 1) {\n      differences.push({\n        field: 'document_type',\n        type: 'changed',\n        oldValue: docTypes[0],\n        newValue: docTypes[1],\n        significance: 'medium',\n        description: `Document types differ: ${docTypes.join(' vs ')}`\n      });\n    }\n\n    // Basic comparison of file sizes\n    const sizes = documents.map(d => d.statistics.totalCharacters);\n    const sizeDiff = Math.abs(sizes[0] - sizes[1]) / Math.max(sizes[0], sizes[1]);\n    if (sizeDiff > 0.2) {\n      differences.push({\n        field: 'content_length',\n        type: 'changed',\n        oldValue: sizes[0],\n        newValue: sizes[1],\n        significance: 'low',\n        description: `Significant difference in document length: ${sizeDiff > 0.5 ? 'major' : 'moderate'} variation`\n      });\n    }\n\n    return {\n      differences,\n      summary: `Basic comparison of ${documents.length} documents completed. AI analysis unavailable.`,\n      recommendations: [\n        'Review documents manually for detailed comparison',\n        'Check pricing and terms carefully',\n        'Verify vendor information and credentials'\n      ]\n    };\n  }\n\n  private static detectComparisonType(documents: ExtractedPDFData[]): 'quotation' | 'contract' | 'invoice' | 'general' {\n    const types = documents.map(d => d.aiAnalysis?.documentType).filter(Boolean);\n\n    if (types.every(type => type === 'quotation')) return 'quotation';\n    if (types.every(type => type === 'contract')) return 'contract';\n    if (types.every(type => type === 'invoice')) return 'invoice';\n\n    return 'general';\n  }\n\n  private static generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  // Generate side-by-side comparison with scoring\n  static async generateSideBySideComparison(documents: ExtractedPDFData[]): Promise<SideBySideComparison> {\n    try {\n      const scores = await this.scoreQuotations(documents);\n      const comparisonMatrix = this.buildComparisonMatrix(documents);\n      const bestChoice = this.determineBestChoice(scores);\n\n      return {\n        documents,\n        scores,\n        bestChoice,\n        comparisonMatrix\n      };\n    } catch (error) {\n      console.error('Side-by-side comparison error:', error);\n      return this.createFallbackSideBySide(documents);\n    }\n  }\n\n  private static async scoreQuotations(documents: ExtractedPDFData[]): Promise<QuotationScore[]> {\n    try {\n      const prompt = this.createScoringPrompt(documents);\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4o-mini\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are an expert procurement analyst. Score and evaluate quotations based on pricing, terms, vendor reliability, timeline, and completeness. Provide detailed scoring and recommendations.\"\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        temperature: 0.1,\n        max_tokens: 2500,\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('No response from OpenAI');\n      }\n\n      const analysis = JSON.parse(response);\n      return this.validateQuotationScores(analysis.scores, documents);\n    } catch (error) {\n      console.error('Quotation scoring error:', error);\n      return this.createFallbackScores(documents);\n    }\n  }\n\n  private static createScoringPrompt(documents: ExtractedPDFData[]): string {\n    const documentSummaries = documents.map((doc, index) => {\n      const quotationDetails = doc.aiAnalysis?.quotationDetails;\n      return `\nDocument ${index + 1}: ${doc.fileName}\n- Total Amount: ${quotationDetails?.totals?.total || 'Not specified'} ${quotationDetails?.totals?.currency || ''}\n- Vendor: ${quotationDetails?.vendor?.name || 'Not specified'}\n- Valid Until: ${quotationDetails?.validUntil || 'Not specified'}\n- Items Count: ${quotationDetails?.items?.length || 0}\n- Terms: ${quotationDetails?.terms?.join('; ') || 'Not specified'}\n- Content Quality: ${doc.statistics.totalWords} words, ${doc.statistics.totalPages} pages\n`;\n    }).join('\\n');\n\n    return `\nScore and evaluate the following ${documents.length} quotations for a procurement decision.\n\n${documentSummaries}\n\nProvide scoring in this exact JSON format:\n{\n  \"scores\": [\n    {\n      \"documentId\": \"document_id\",\n      \"fileName\": \"filename\",\n      \"totalScore\": 85,\n      \"scores\": {\n        \"pricing\": 90,\n        \"terms\": 80,\n        \"vendor\": 85,\n        \"timeline\": 85,\n        \"completeness\": 90\n      },\n      \"strengths\": [\"Lower total cost\", \"Flexible payment terms\", \"Fast delivery\"],\n      \"weaknesses\": [\"Unknown vendor reputation\", \"Limited warranty\"],\n      \"recommendation\": \"best|good|acceptable|poor\"\n    }\n  ]\n}\n\nScoring criteria (0-100):\n- PRICING: Lower cost = higher score, consider value for money\n- TERMS: Better payment terms, warranties, conditions = higher score\n- VENDOR: Reputation, reliability, past performance = higher score\n- TIMELINE: Faster delivery, realistic schedules = higher score\n- COMPLETENESS: Detailed specifications, clear terms = higher score\n\nCalculate totalScore as weighted average: pricing(30%) + terms(25%) + vendor(20%) + timeline(15%) + completeness(10%)\n\nReturn only valid JSON without markdown formatting.\n`;\n  }\n\n  // Helper method to get detailed comparison analysis\n  static async getDetailedAnalysis(comparison: PDFComparison): Promise<ComparisonAnalysis> {\n    const differences = comparison.differences;\n\n    // Generate side-by-side comparison if quotation type\n    let sideBySide: SideBySideComparison | undefined;\n    if (comparison.comparisonType === 'quotation' && comparison.documents.length >= 2) {\n      sideBySide = await this.generateSideBySideComparison(comparison.documents);\n    }\n\n    return {\n      pricing: {\n        differences: differences.filter(d => d.field.includes('pricing') || d.field.includes('cost') || d.field.includes('total')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('pricing')))\n      },\n      terms: {\n        differences: differences.filter(d => d.field.includes('terms') || d.field.includes('condition')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('terms')))\n      },\n      vendors: {\n        differences: differences.filter(d => d.field.includes('vendor') || d.field.includes('supplier')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('vendor')))\n      },\n      timeline: {\n        differences: differences.filter(d => d.field.includes('date') || d.field.includes('timeline') || d.field.includes('delivery')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('date')))\n      },\n      overall: {\n        recommendation: comparison.summary,\n        bestOption: this.determineBestOption(comparison),\n        riskFactors: this.identifyRiskFactors(comparison)\n      },\n      sideBySide\n    };\n  }\n\n  private static summarizeDifferences(differences: ComparisonDifference[]): string {\n    if (differences.length === 0) return 'No significant differences found';\n\n    const highImpact = differences.filter(d => d.significance === 'high').length;\n    const mediumImpact = differences.filter(d => d.significance === 'medium').length;\n\n    return `Found ${differences.length} difference(s): ${highImpact} high impact, ${mediumImpact} medium impact`;\n  }\n\n  private static determineBestOption(comparison: PDFComparison): string | undefined {\n    // Simple logic to determine best option based on differences\n    const highImpactDiffs = comparison.differences.filter(d => d.significance === 'high');\n    if (highImpactDiffs.length === 0) {\n      return 'Options are comparable - review based on your priorities';\n    }\n\n    return 'Manual review recommended due to significant differences';\n  }\n\n  private static identifyRiskFactors(comparison: PDFComparison): string[] {\n    const risks: string[] = [];\n\n    const highImpactDiffs = comparison.differences.filter(d => d.significance === 'high');\n    if (highImpactDiffs.length > 0) {\n      risks.push(`${highImpactDiffs.length} high-impact differences require attention`);\n    }\n\n    const pricingDiffs = comparison.differences.filter(d => d.field.includes('pricing'));\n    if (pricingDiffs.length > 0) {\n      risks.push('Pricing variations detected - verify total costs');\n    }\n\n    return risks;\n  }\n\n  private static validateQuotationScores(scores: any[], documents: ExtractedPDFData[]): QuotationScore[] {\n    return documents.map((doc, index) => {\n      const score = scores[index] || {};\n      return {\n        documentId: doc.id,\n        fileName: doc.fileName,\n        totalScore: Math.min(Math.max(score.totalScore || 50, 0), 100),\n        scores: {\n          pricing: Math.min(Math.max(score.scores?.pricing || 50, 0), 100),\n          terms: Math.min(Math.max(score.scores?.terms || 50, 0), 100),\n          vendor: Math.min(Math.max(score.scores?.vendor || 50, 0), 100),\n          timeline: Math.min(Math.max(score.scores?.timeline || 50, 0), 100),\n          completeness: Math.min(Math.max(score.scores?.completeness || 50, 0), 100)\n        },\n        strengths: Array.isArray(score.strengths) ? score.strengths : ['Analysis pending'],\n        weaknesses: Array.isArray(score.weaknesses) ? score.weaknesses : ['Analysis pending'],\n        recommendation: ['best', 'good', 'acceptable', 'poor'].includes(score.recommendation)\n          ? score.recommendation : 'acceptable'\n      };\n    });\n  }\n\n  private static createFallbackScores(documents: ExtractedPDFData[]): QuotationScore[] {\n    return documents.map((doc, index) => ({\n      documentId: doc.id,\n      fileName: doc.fileName,\n      totalScore: 70 - (index * 5), // Simple ranking\n      scores: {\n        pricing: 70,\n        terms: 70,\n        vendor: 70,\n        timeline: 70,\n        completeness: 70\n      },\n      strengths: ['Document processed successfully'],\n      weaknesses: ['AI analysis unavailable'],\n      recommendation: index === 0 ? 'good' : 'acceptable' as any\n    }));\n  }\n\n  private static buildComparisonMatrix(documents: ExtractedPDFData[]) {\n    const matrix: any[] = [];\n\n    // Extract key fields for comparison\n    const fields = [\n      { key: 'total_amount', category: 'pricing', label: 'Total Amount' },\n      { key: 'vendor_name', category: 'vendor', label: 'Vendor Name' },\n      { key: 'valid_until', category: 'timeline', label: 'Valid Until' },\n      { key: 'payment_terms', category: 'terms', label: 'Payment Terms' },\n      { key: 'delivery_time', category: 'timeline', label: 'Delivery Time' },\n      { key: 'warranty', category: 'terms', label: 'Warranty' }\n    ];\n\n    fields.forEach(field => {\n      const values = documents.map(doc => {\n        const quotation = doc.aiAnalysis?.quotationDetails;\n        let value = 'Not specified';\n\n        switch (field.key) {\n          case 'total_amount':\n            value = quotation?.totals?.total ?\n              `${quotation.totals.total} ${quotation.totals.currency || ''}` : 'Not specified';\n            break;\n          case 'vendor_name':\n            value = quotation?.vendor?.name || 'Not specified';\n            break;\n          case 'valid_until':\n            value = quotation?.validUntil || 'Not specified';\n            break;\n          case 'payment_terms':\n            value = quotation?.terms?.find(t => t.toLowerCase().includes('payment')) || 'Not specified';\n            break;\n          case 'delivery_time':\n            value = quotation?.terms?.find(t => t.toLowerCase().includes('delivery')) || 'Not specified';\n            break;\n          case 'warranty':\n            value = quotation?.terms?.find(t => t.toLowerCase().includes('warranty')) || 'Not specified';\n            break;\n        }\n\n        return {\n          documentId: doc.id,\n          value,\n          isBest: false,\n          isWorst: false,\n          score: 70 // Default score\n        };\n      });\n\n      // Determine best/worst for this field\n      if (field.category === 'pricing') {\n        // For pricing, lower is better\n        const amounts = values.map(v => {\n          const match = v.value.match(/[\\d,]+\\.?\\d*/);\n          return match ? parseFloat(match[0].replace(/,/g, '')) : Infinity;\n        });\n        const minAmount = Math.min(...amounts);\n        const maxAmount = Math.max(...amounts);\n\n        values.forEach((v, i) => {\n          if (amounts[i] === minAmount && minAmount !== Infinity) v.isBest = true;\n          if (amounts[i] === maxAmount && maxAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;\n          v.score = amounts[i] === Infinity ? 50 : Math.round(100 - ((amounts[i] - minAmount) / (maxAmount - minAmount)) * 50);\n        });\n      } else {\n        // For other fields, mark first non-empty as best\n        const firstValid = values.find(v => v.value !== 'Not specified');\n        if (firstValid) firstValid.isBest = true;\n\n        values.forEach(v => {\n          v.score = v.value !== 'Not specified' ? 80 : 30;\n        });\n      }\n\n      matrix.push({\n        field: field.label,\n        category: field.category,\n        values\n      });\n    });\n\n    return matrix;\n  }\n\n  private static determineBestChoice(scores: QuotationScore[]) {\n    const bestScore = scores.reduce((best, current) =>\n      current.totalScore > best.totalScore ? current : best\n    );\n\n    return {\n      documentId: bestScore.documentId,\n      reason: `Highest overall score with strong performance in ${bestScore.strengths.slice(0, 2).join(' and ')}`,\n      confidence: Math.min(bestScore.totalScore / 100, 0.95)\n    };\n  }\n\n  private static createFallbackSideBySide(documents: ExtractedPDFData[]): SideBySideComparison {\n    const scores = this.createFallbackScores(documents);\n    const comparisonMatrix = this.buildComparisonMatrix(documents);\n    const bestChoice = this.determineBestChoice(scores);\n\n    return {\n      documents,\n      scores,\n      bestChoice,\n      comparisonMatrix\n    };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGA,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEO,MAAM;IACX,aAAa,YAAY,SAA6B,EAA0B;QAC9E,IAAI;YACF,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,qBAAqB,MAAM,IAAI,CAAC,mBAAmB,CAAC;YAE1D,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB;gBACA,aAAa,mBAAmB,WAAW;gBAC3C,SAAS,mBAAmB,OAAO;gBACnC,iBAAiB,mBAAmB,eAAe;gBACnD,YAAY,IAAI,OAAO,WAAW;gBAClC,gBAAgB,IAAI,CAAC,oBAAoB,CAAC;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,aAAqB,oBAAoB,SAA6B,EAInE;QACD,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;YAE3C,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACtD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;YACjD,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,OAAO,IAAI,CAAC,0BAA0B,CAAC;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACvC;IACF;IAEA,OAAe,uBAAuB,SAA6B,EAAU;QAC3E,MAAM,oBAAoB,UAAU,GAAG,CAAC,CAAC,KAAK;YAC5C,MAAM,mBAAmB,IAAI,UAAU,EAAE;YACzC,OAAO,CAAC;SACL,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC;iBACrB,EAAE,IAAI,UAAU,EAAE,gBAAgB,UAAU;mBAC1C,EAAE,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,MAAM;qBACxC,EAAE,mBAAmB,KAAK,SAAS,CAAC,kBAAkB,MAAM,KAAK,iBAAiB;gBACvF,EAAE,IAAI,UAAU,EAAE,aAAa,KAAK,SAAS,OAAO;AACpE,CAAC;QACG,GAAG,IAAI,CAAC;QAER,OAAO,CAAC;sBACU,EAAE,UAAU,MAAM,CAAC;;AAEzC,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCpB,CAAC;IACC;IAEA,OAAe,2BAA2B,QAAa,EAIrD;QACA,OAAO;YACL,aAAa,MAAM,OAAO,CAAC,SAAS,WAAW,IAC3C,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBACzC,OAAO,KAAK,KAAK,IAAI;oBACrB,MAAM,KAAK,IAAI,IAAI;oBACnB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY,IAAI;oBACnC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,KACC,EAAE;YACN,SAAS,SAAS,OAAO,IAAI;YAC7B,iBAAiB,MAAM,OAAO,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,GAAG,EAAE;QAC1F;IACF;IAEA,OAAe,yBAAyB,SAA6B,EAInE;QACA,MAAM,cAAsC,EAAE;QAE9C,qCAAqC;QACrC,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,gBAAgB;QAClE,IAAI,IAAI,IAAI,UAAU,IAAI,GAAG,GAAG;YAC9B,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,MAAM;gBACN,UAAU,QAAQ,CAAC,EAAE;gBACrB,UAAU,QAAQ,CAAC,EAAE;gBACrB,cAAc;gBACd,aAAa,CAAC,uBAAuB,EAAE,SAAS,IAAI,CAAC,SAAS;YAChE;QACF;QAEA,iCAAiC;QACjC,MAAM,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,eAAe;QAC7D,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;QAC5E,IAAI,WAAW,KAAK;YAClB,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,MAAM;gBACN,UAAU,KAAK,CAAC,EAAE;gBAClB,UAAU,KAAK,CAAC,EAAE;gBAClB,cAAc;gBACd,aAAa,CAAC,2CAA2C,EAAE,WAAW,MAAM,UAAU,WAAW,UAAU,CAAC;YAC9G;QACF;QAEA,OAAO;YACL;YACA,SAAS,CAAC,oBAAoB,EAAE,UAAU,MAAM,CAAC,8CAA8C,CAAC;YAChG,iBAAiB;gBACf;gBACA;gBACA;aACD;QACH;IACF;IAEA,OAAe,qBAAqB,SAA6B,EAAoD;QACnH,MAAM,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,cAAc,MAAM,CAAC;QAEpE,IAAI,MAAM,KAAK,CAAC,CAAA,OAAQ,SAAS,cAAc,OAAO;QACtD,IAAI,MAAM,KAAK,CAAC,CAAA,OAAQ,SAAS,aAAa,OAAO;QACrD,IAAI,MAAM,KAAK,CAAC,CAAA,OAAQ,SAAS,YAAY,OAAO;QAEpD,OAAO;IACT;IAEA,OAAe,aAAqB;QAClC,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA,gDAAgD;IAChD,aAAa,6BAA6B,SAA6B,EAAiC;QACtG,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,CAAC;YAC1C,MAAM,mBAAmB,IAAI,CAAC,qBAAqB,CAAC;YACpD,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAE5C,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACvC;IACF;IAEA,aAAqB,gBAAgB,SAA6B,EAA6B;QAC7F,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC;YAExC,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACtD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;YACjD,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,MAAM,EAAE;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;IACF;IAEA,OAAe,oBAAoB,SAA6B,EAAU;QACxE,MAAM,oBAAoB,UAAU,GAAG,CAAC,CAAC,KAAK;YAC5C,MAAM,mBAAmB,IAAI,UAAU,EAAE;YACzC,OAAO,CAAC;SACL,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC;gBACtB,EAAE,kBAAkB,QAAQ,SAAS,gBAAgB,CAAC,EAAE,kBAAkB,QAAQ,YAAY,GAAG;UACvG,EAAE,kBAAkB,QAAQ,QAAQ,gBAAgB;eAC/C,EAAE,kBAAkB,cAAc,gBAAgB;eAClD,EAAE,kBAAkB,OAAO,UAAU,EAAE;SAC7C,EAAE,kBAAkB,OAAO,KAAK,SAAS,gBAAgB;mBAC/C,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC;AACnF,CAAC;QACG,GAAG,IAAI,CAAC;QAER,OAAO,CAAC;iCACqB,EAAE,UAAU,MAAM,CAAC;;AAEpD,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCpB,CAAC;IACC;IAEA,oDAAoD;IACpD,aAAa,oBAAoB,UAAyB,EAA+B;QACvF,MAAM,cAAc,WAAW,WAAW;QAE1C,qDAAqD;QACrD,IAAI;QACJ,IAAI,WAAW,cAAc,KAAK,eAAe,WAAW,SAAS,CAAC,MAAM,IAAI,GAAG;YACjF,aAAa,MAAM,IAAI,CAAC,4BAA4B,CAAC,WAAW,SAAS;QAC3E;QAEA,OAAO;YACL,SAAS;gBACP,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC;gBACjH,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,OAAO;gBACL,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC;gBACnF,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,SAAS;gBACP,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC;gBACpF,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,UAAU;gBACR,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,QAAQ,CAAC;gBAClH,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,SAAS;gBACP,gBAAgB,WAAW,OAAO;gBAClC,YAAY,IAAI,CAAC,mBAAmB,CAAC;gBACrC,aAAa,IAAI,CAAC,mBAAmB,CAAC;YACxC;YACA;QACF;IACF;IAEA,OAAe,qBAAqB,WAAmC,EAAU;QAC/E,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO;QAErC,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,QAAQ,MAAM;QAC5E,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,UAAU,MAAM;QAEhF,OAAO,CAAC,MAAM,EAAE,YAAY,MAAM,CAAC,gBAAgB,EAAE,WAAW,cAAc,EAAE,aAAa,cAAc,CAAC;IAC9G;IAEA,OAAe,oBAAoB,UAAyB,EAAsB;QAChF,6DAA6D;QAC7D,MAAM,kBAAkB,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;QAC9E,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,OAAe,oBAAoB,UAAyB,EAAY;QACtE,MAAM,QAAkB,EAAE;QAE1B,MAAM,kBAAkB,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;QAC9E,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,CAAC,GAAG,gBAAgB,MAAM,CAAC,0CAA0C,CAAC;QAClF;QAEA,MAAM,eAAe,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;QACzE,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,OAAe,wBAAwB,MAAa,EAAE,SAA6B,EAAoB;QACrG,OAAO,UAAU,GAAG,CAAC,CAAC,KAAK;YACzB,MAAM,QAAQ,MAAM,CAAC,MAAM,IAAI,CAAC;YAChC,OAAO;gBACL,YAAY,IAAI,EAAE;gBAClB,UAAU,IAAI,QAAQ;gBACtB,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,UAAU,IAAI,IAAI,IAAI;gBAC1D,QAAQ;oBACN,SAAS,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,WAAW,IAAI,IAAI;oBAC5D,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,SAAS,IAAI,IAAI;oBACxD,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,UAAU,IAAI,IAAI;oBAC1D,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,YAAY,IAAI,IAAI;oBAC9D,cAAc,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,gBAAgB,IAAI,IAAI;gBACxE;gBACA,WAAW,MAAM,OAAO,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,GAAG;oBAAC;iBAAmB;gBAClF,YAAY,MAAM,OAAO,CAAC,MAAM,UAAU,IAAI,MAAM,UAAU,GAAG;oBAAC;iBAAmB;gBACrF,gBAAgB;oBAAC;oBAAQ;oBAAQ;oBAAc;iBAAO,CAAC,QAAQ,CAAC,MAAM,cAAc,IAChF,MAAM,cAAc,GAAG;YAC7B;QACF;IACF;IAEA,OAAe,qBAAqB,SAA6B,EAAoB;QACnF,OAAO,UAAU,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;gBACpC,YAAY,IAAI,EAAE;gBAClB,UAAU,IAAI,QAAQ;gBACtB,YAAY,KAAM,QAAQ;gBAC1B,QAAQ;oBACN,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,cAAc;gBAChB;gBACA,WAAW;oBAAC;iBAAkC;gBAC9C,YAAY;oBAAC;iBAA0B;gBACvC,gBAAgB,UAAU,IAAI,SAAS;YACzC,CAAC;IACH;IAEA,OAAe,sBAAsB,SAA6B,EAAE;QAClE,MAAM,SAAgB,EAAE;QAExB,oCAAoC;QACpC,MAAM,SAAS;YACb;gBAAE,KAAK;gBAAgB,UAAU;gBAAW,OAAO;YAAe;YAClE;gBAAE,KAAK;gBAAe,UAAU;gBAAU,OAAO;YAAc;YAC/D;gBAAE,KAAK;gBAAe,UAAU;gBAAY,OAAO;YAAc;YACjE;gBAAE,KAAK;gBAAiB,UAAU;gBAAS,OAAO;YAAgB;YAClE;gBAAE,KAAK;gBAAiB,UAAU;gBAAY,OAAO;YAAgB;YACrE;gBAAE,KAAK;gBAAY,UAAU;gBAAS,OAAO;YAAW;SACzD;QAED,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,SAAS,UAAU,GAAG,CAAC,CAAA;gBAC3B,MAAM,YAAY,IAAI,UAAU,EAAE;gBAClC,IAAI,QAAQ;gBAEZ,OAAQ,MAAM,GAAG;oBACf,KAAK;wBACH,QAAQ,WAAW,QAAQ,QACzB,GAAG,UAAU,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM,CAAC,QAAQ,IAAI,IAAI,GAAG;wBACnE;oBACF,KAAK;wBACH,QAAQ,WAAW,QAAQ,QAAQ;wBACnC;oBACF,KAAK;wBACH,QAAQ,WAAW,cAAc;wBACjC;oBACF,KAAK;wBACH,QAAQ,WAAW,OAAO,KAAK,CAAA,IAAK,EAAE,WAAW,GAAG,QAAQ,CAAC,eAAe;wBAC5E;oBACF,KAAK;wBACH,QAAQ,WAAW,OAAO,KAAK,CAAA,IAAK,EAAE,WAAW,GAAG,QAAQ,CAAC,gBAAgB;wBAC7E;oBACF,KAAK;wBACH,QAAQ,WAAW,OAAO,KAAK,CAAA,IAAK,EAAE,WAAW,GAAG,QAAQ,CAAC,gBAAgB;wBAC7E;gBACJ;gBAEA,OAAO;oBACL,YAAY,IAAI,EAAE;oBAClB;oBACA,QAAQ;oBACR,SAAS;oBACT,OAAO,GAAG,gBAAgB;gBAC5B;YACF;YAEA,sCAAsC;YACtC,IAAI,MAAM,QAAQ,KAAK,WAAW;gBAChC,+BAA+B;gBAC/B,MAAM,UAAU,OAAO,GAAG,CAAC,CAAA;oBACzB,MAAM,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;oBAC5B,OAAO,QAAQ,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,OAAO;gBAC1D;gBACA,MAAM,YAAY,KAAK,GAAG,IAAI;gBAC9B,MAAM,YAAY,KAAK,GAAG,IAAI;gBAE9B,OAAO,OAAO,CAAC,CAAC,GAAG;oBACjB,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,UAAU,EAAE,MAAM,GAAG;oBACnE,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,YAAY,cAAc,WAAW,EAAE,OAAO,GAAG;oBAC/F,EAAE,KAAK,GAAG,OAAO,CAAC,EAAE,KAAK,WAAW,KAAK,KAAK,KAAK,CAAC,MAAM,AAAC,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,IAAI,CAAC,YAAY,SAAS,IAAK;gBACnH;YACF,OAAO;gBACL,iDAAiD;gBACjD,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;gBAChD,IAAI,YAAY,WAAW,MAAM,GAAG;gBAEpC,OAAO,OAAO,CAAC,CAAA;oBACb,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,kBAAkB,KAAK;gBAC/C;YACF;YAEA,OAAO,IAAI,CAAC;gBACV,OAAO,MAAM,KAAK;gBAClB,UAAU,MAAM,QAAQ;gBACxB;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,oBAAoB,MAAwB,EAAE;QAC3D,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,MAAM,UACrC,QAAQ,UAAU,GAAG,KAAK,UAAU,GAAG,UAAU;QAGnD,OAAO;YACL,YAAY,UAAU,UAAU;YAChC,QAAQ,CAAC,iDAAiD,EAAE,UAAU,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU;YAC3G,YAAY,KAAK,GAAG,CAAC,UAAU,UAAU,GAAG,KAAK;QACnD;IACF;IAEA,OAAe,yBAAyB,SAA6B,EAAwB;QAC3F,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC;QACzC,MAAM,mBAAmB,IAAI,CAAC,qBAAqB,CAAC;QACpD,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAE5C,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/api/detailed-analysis/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { PDFComparison } from '@/types/pdf';\nimport { PDFComparator } from '@/services/pdfComparator';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { comparison } = body;\n\n    if (!comparison || !comparison.documents) {\n      return NextResponse.json(\n        { error: 'Invalid comparison data' },\n        { status: 400 }\n      );\n    }\n\n    const detailedAnalysis = await PDFComparator.getDetailedAnalysis(comparison);\n    \n    return NextResponse.json(detailedAnalysis);\n  } catch (error) {\n    console.error('Detailed analysis error:', error);\n    return NextResponse.json(\n      { error: 'Failed to generate detailed analysis' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,IAAI,CAAC,cAAc,CAAC,WAAW,SAAS,EAAE;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,mBAAmB,MAAM,kIAAA,CAAA,gBAAa,CAAC,mBAAmB,CAAC;QAEjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}