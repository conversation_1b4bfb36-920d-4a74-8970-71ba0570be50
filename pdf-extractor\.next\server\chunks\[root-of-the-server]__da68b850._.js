module.exports = {

"[project]/.next-internal/server/app/api/extract-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$pdf$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/pdf-parse/index.js [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        if (file.type !== 'application/pdf') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be a PDF'
            }, {
                status: 400
            });
        }
        if (file.size > 10 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File size must be less than 10MB'
            }, {
                status: 400
            });
        }
        const startTime = Date.now();
        // Convert file to buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Parse PDF
        const pdfData = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$pdf$2d$parse$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__(buffer);
        // Extract metadata
        const metadata = extractMetadata(pdfData, file);
        // Extract content and structure
        const content = extractContent(pdfData);
        // Calculate statistics
        const statistics = calculateStatistics(pdfData, content, file, startTime);
        const result = {
            metadata,
            content,
            statistics,
            fileName: file.name,
            extractedAt: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('PDF extraction error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to extract PDF data. Please ensure the file is a valid PDF.'
        }, {
            status: 500
        });
    }
}
function extractMetadata(pdfData, file) {
    const info = pdfData.info || {};
    return {
        title: info.Title || 'Unknown',
        author: info.Author || 'Unknown',
        subject: info.Subject || 'Not specified',
        creator: info.Creator || 'Unknown',
        producer: info.Producer || 'Unknown',
        creationDate: info.CreationDate ? new Date(info.CreationDate).toLocaleDateString() : 'Unknown',
        modificationDate: info.ModDate ? new Date(info.ModDate).toLocaleDateString() : 'Unknown',
        keywords: info.Keywords || 'None',
        pages: pdfData.numpages || 0
    };
}
function extractContent(pdfData) {
    const fullText = pdfData.text || '';
    const lines = fullText.split('\n').filter((line)=>line.trim());
    // Simple structure extraction
    const headings = lines.filter((line)=>line.length < 100 && (line.match(/^[A-Z\s]+$/) || line.match(/^\d+\.?\s+[A-Z]/)));
    const paragraphs = lines.filter((line)=>line.length > 50 && !headings.includes(line));
    // Extract potential tables (lines with multiple spaces or tabs)
    const tables = lines.filter((line)=>line.includes('\t') || line.match(/\s{3,}/));
    // Extract lists (lines starting with bullets or numbers)
    const lists = lines.filter((line)=>line.match(/^[\s]*[-•*]\s/) || line.match(/^[\s]*\d+[\.)]\s/));
    // Split text by pages (approximate)
    const pageTexts = splitTextByPages(fullText, pdfData.numpages);
    return {
        fullText,
        pageTexts,
        structure: {
            headings: headings.slice(0, 20),
            paragraphs: paragraphs.slice(0, 10),
            tables: tables.slice(0, 10),
            lists: lists.slice(0, 15)
        }
    };
}
function splitTextByPages(text, numPages) {
    if (numPages <= 1) return [
        text
    ];
    const lines = text.split('\n');
    const linesPerPage = Math.ceil(lines.length / numPages);
    const pages = [];
    for(let i = 0; i < numPages; i++){
        const start = i * linesPerPage;
        const end = Math.min((i + 1) * linesPerPage, lines.length);
        pages.push(lines.slice(start, end).join('\n'));
    }
    return pages;
}
function calculateStatistics(pdfData, content, file, startTime) {
    const totalCharacters = content.fullText.length;
    const totalWords = content.fullText.split(/\s+/).filter((word)=>word.length > 0).length;
    const totalLines = content.fullText.split('\n').length;
    const totalPages = pdfData.numpages || 1;
    const processingTime = Date.now() - startTime;
    return {
        totalPages,
        totalCharacters,
        totalWords,
        totalLines,
        averageWordsPerPage: Math.round(totalWords / totalPages),
        fileSize: formatFileSize(file.size),
        processingTime
    };
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__da68b850._.js.map