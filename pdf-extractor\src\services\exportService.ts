import { ExtractedPDFData } from '@/types/pdf';

export class ExportService {
  static exportAsJSON(data: ExtractedPDFData): void {
    const jsonString = JSON.stringify(data, null, 2);
    this.downloadFile(jsonString, `${data.fileName}_extracted.json`, 'application/json');
  }

  static exportAsCSV(data: ExtractedPDFData): void {
    const csvContent = this.convertToCSV(data);
    this.downloadFile(csvContent, `${data.fileName}_extracted.csv`, 'text/csv');
  }

  static exportAsText(data: ExtractedPDFData): void {
    const textContent = this.convertToText(data);
    this.downloadFile(textContent, `${data.fileName}_extracted.txt`, 'text/plain');
  }

  private static convertToCSV(data: ExtractedPDFData): string {
    const rows: string[] = [];

    // Header
    rows.push('Category,Field,Value');

    // Metadata
    Object.entries(data.metadata).forEach(([key, value]) => {
      rows.push(`Metadata,${key},"${String(value).replace(/"/g, '""')}"`);
    });

    // Statistics
    Object.entries(data.statistics).forEach(([key, value]) => {
      rows.push(`Statistics,${key},"${String(value).replace(/"/g, '""')}"`);
    });

    // Content structure counts
    Object.entries(data.content.structure).forEach(([key, items]) => {
      rows.push(`Content Structure,${key} Count,${items.length}`);
    });

    // AI Analysis data
    if (data.aiAnalysis) {
      rows.push(`AI Analysis,Document Type,"${data.aiAnalysis.documentType}"`);
      rows.push(`AI Analysis,Confidence,${data.aiAnalysis.confidence}`);
      rows.push(`AI Analysis,Summary,"${data.aiAnalysis.summary.replace(/"/g, '""')}"`);

      if (data.aiAnalysis.quotationDetails?.quotationNumber) {
        rows.push(`AI Analysis,Quote Number,"${data.aiAnalysis.quotationDetails.quotationNumber}"`);
      }
      if (data.aiAnalysis.quotationDetails?.totals?.total) {
        rows.push(`AI Analysis,Total Amount,${data.aiAnalysis.quotationDetails.totals.total}`);
      }
    }

    // Add some sample content
    rows.push(`Content,Full Text Length,${data.content.fullText.length}`);
    rows.push(`Content,Sample Text,"${data.content.fullText.substring(0, 200).replace(/"/g, '""')}..."`);

    return rows.join('\n');
  }

  private static convertToText(data: ExtractedPDFData): string {
    const sections: string[] = [];

    // Header
    sections.push('PDF EXTRACTION REPORT');
    sections.push('='.repeat(50));
    sections.push(`File: ${data.fileName}`);
    sections.push(`Extracted: ${new Date(data.extractedAt).toLocaleString()}`);
    sections.push('');

    // Metadata
    sections.push('METADATA');
    sections.push('-'.repeat(20));
    Object.entries(data.metadata).forEach(([key, value]) => {
      sections.push(`${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`);
    });
    sections.push('');

    // Statistics
    sections.push('STATISTICS');
    sections.push('-'.repeat(20));
    Object.entries(data.statistics).forEach(([key, value]) => {
      const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      sections.push(`${label}: ${value}`);
    });
    sections.push('');

    // AI Analysis
    if (data.aiAnalysis) {
      sections.push('AI ANALYSIS');
      sections.push('-'.repeat(20));
      sections.push(`Document Type: ${data.aiAnalysis.documentType}`);
      sections.push(`Confidence: ${(data.aiAnalysis.confidence * 100).toFixed(1)}%`);
      sections.push(`Summary: ${data.aiAnalysis.summary}`);
      sections.push('');

      if (data.aiAnalysis.quotationDetails) {
        sections.push('QUOTATION DETAILS');
        sections.push('-'.repeat(20));
        const qd = data.aiAnalysis.quotationDetails;
        if (qd.quotationNumber) sections.push(`Quote Number: ${qd.quotationNumber}`);
        if (qd.date) sections.push(`Date: ${qd.date}`);
        if (qd.validUntil) sections.push(`Valid Until: ${qd.validUntil}`);
        if (qd.vendor?.name) sections.push(`Vendor: ${qd.vendor.name}`);
        if (qd.customer?.name) sections.push(`Customer: ${qd.customer.name}`);
        if (qd.totals?.total) sections.push(`Total: ${qd.totals.total} ${qd.totals.currency || ''}`);
        sections.push('');
      }

      if (data.aiAnalysis.keyInsights.length > 0) {
        sections.push('KEY INSIGHTS');
        sections.push('-'.repeat(20));
        data.aiAnalysis.keyInsights.forEach((insight, index) => {
          sections.push(`${index + 1}. ${insight}`);
        });
        sections.push('');
      }
    }

    // Content Structure
    sections.push('CONTENT STRUCTURE');
    sections.push('-'.repeat(20));
    Object.entries(data.content.structure).forEach(([key, items]) => {
      if (items.length > 0) {
        sections.push(`${key.toUpperCase()} (${items.length} items):`);
        items.slice(0, 5).forEach((item, index) => {
          sections.push(`  ${index + 1}. ${item.substring(0, 100)}${item.length > 100 ? '...' : ''}`);
        });
        if (items.length > 5) {
          sections.push(`  ... and ${items.length - 5} more items`);
        }
        sections.push('');
      }
    });

    // Full Text (truncated)
    sections.push('FULL TEXT (First 1000 characters)');
    sections.push('-'.repeat(40));
    sections.push(data.content.fullText.substring(0, 1000));
    if (data.content.fullText.length > 1000) {
      sections.push('\n... (text truncated)');
    }

    return sections.join('\n');
  }

  private static downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);
  }
}
