'use client';

import { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';

interface DragAndDropProps {
  onFileUpload: (file: File) => void;
}

export function DragAndDrop({ onFileUpload }: DragAndDropProps) {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      onFileUpload(acceptedFiles[0]);
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: false
  });

  return (
    <div
      {...getRootProps()}
      className={`p-8 border-2 border-dashed rounded-lg text-center cursor-pointer transition-colors
        ${isDragActive 
          ? 'border-blue-500 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
        }`}
    >
      <input {...getInputProps()} />
      {isDragActive ? (
        <p className="text-blue-600">Drop the PDF file here</p>
      ) : (
        <p className="text-gray-600">
          Drag and drop a PDF file here, or click to select a file
        </p>
      )}
    </div>
  );
}
