{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/MultiPDFUploader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, FileText, AlertCircle, X, Plus, GitCompare } from 'lucide-react';\n\ninterface UploadedFile {\n  id: string;\n  file: File;\n  status: 'pending' | 'processing' | 'completed' | 'error';\n  error?: string;\n}\n\ninterface MultiPDFUploaderProps {\n  onFilesUpload: (files: File[]) => void;\n  onCompare: () => void;\n  isProcessing: boolean;\n  uploadedFiles: UploadedFile[];\n  onRemoveFile: (id: string) => void;\n}\n\nexport default function MultiPDFUploader({ \n  onFilesUpload, \n  onCompare, \n  isProcessing, \n  uploadedFiles,\n  onRemoveFile \n}: MultiPDFUploaderProps) {\n  const [error, setError] = useState<string | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError(null);\n    \n    if (rejectedFiles.length > 0) {\n      setError('Please upload valid PDF files only.');\n      return;\n    }\n\n    // Check file sizes\n    const oversizedFiles = acceptedFiles.filter(file => file.size > 10 * 1024 * 1024);\n    if (oversizedFiles.length > 0) {\n      setError('Some files are larger than 10MB. Please choose smaller files.');\n      return;\n    }\n\n    // Check total number of files\n    if (uploadedFiles.length + acceptedFiles.length > 5) {\n      setError('Maximum 5 PDF files can be compared at once.');\n      return;\n    }\n\n    onFilesUpload(acceptedFiles);\n  }, [onFilesUpload, uploadedFiles.length]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    multiple: true,\n    disabled: isProcessing\n  });\n\n  const canCompare = uploadedFiles.length >= 2 && uploadedFiles.every(f => f.status === 'completed');\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto space-y-6\">\n      {/* Upload Area */}\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\n          ${isDragActive \n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400'\n          }\n          ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        <div className=\"flex flex-col items-center space-y-4\">\n          {isProcessing ? (\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          ) : (\n            <Upload className=\"h-12 w-12 text-gray-400\" />\n          )}\n          \n          <div>\n            <p className=\"text-lg font-medium text-gray-700\">\n              {isProcessing \n                ? 'Processing PDFs...' \n                : isDragActive \n                  ? 'Drop the PDF files here' \n                  : 'Drag & drop PDF files here to compare'\n              }\n            </p>\n            {!isProcessing && (\n              <p className=\"text-sm text-gray-500 mt-1\">\n                or click to select files (2-5 PDFs, max 10MB each)\n              </p>\n            )}\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <FileText className=\"h-6 w-6 text-gray-300\" />\n            <GitCompare className=\"h-6 w-6 text-gray-300\" />\n            <FileText className=\"h-6 w-6 text-gray-300\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"p-3 bg-red-50 border border-red-200 rounded-md flex items-center space-x-2\">\n          <AlertCircle className=\"h-5 w-5 text-red-500\" />\n          <span className=\"text-red-700 text-sm\">{error}</span>\n        </div>\n      )}\n\n      {/* Uploaded Files */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">\n            Uploaded Files ({uploadedFiles.length}/5)\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {uploadedFiles.map((uploadedFile) => (\n              <FileCard\n                key={uploadedFile.id}\n                uploadedFile={uploadedFile}\n                onRemove={() => onRemoveFile(uploadedFile.id)}\n              />\n            ))}\n          </div>\n\n          {/* Compare Button */}\n          <div className=\"flex justify-center pt-4\">\n            <button\n              onClick={onCompare}\n              disabled={!canCompare || isProcessing}\n              className={`\n                flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors\n                ${canCompare && !isProcessing\n                  ? 'bg-blue-600 text-white hover:bg-blue-700'\n                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                }\n              `}\n            >\n              <GitCompare className=\"h-5 w-5\" />\n              <span>\n                {isProcessing \n                  ? 'Processing...' \n                  : canCompare \n                    ? `Compare ${uploadedFiles.length} PDFs`\n                    : `Upload ${2 - uploadedFiles.filter(f => f.status === 'completed').length} more PDF${uploadedFiles.filter(f => f.status === 'completed').length === 1 ? '' : 's'} to compare`\n                }\n              </span>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n// File Card Component\nfunction FileCard({ uploadedFile, onRemove }: { uploadedFile: UploadedFile, onRemove: () => void }) {\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed': return 'bg-green-100 text-green-800';\n      case 'processing': return 'bg-blue-100 text-blue-800';\n      case 'error': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'completed': return 'Ready';\n      case 'processing': return 'Processing...';\n      case 'error': return 'Error';\n      default: return 'Pending';\n    }\n  };\n\n  return (\n    <div className=\"bg-white border rounded-lg p-4 relative\">\n      <button\n        onClick={onRemove}\n        className=\"absolute top-2 right-2 p-1 hover:bg-gray-100 rounded-full\"\n      >\n        <X className=\"h-4 w-4 text-gray-500\" />\n      </button>\n      \n      <div className=\"flex items-start space-x-3\">\n        <FileText className=\"h-8 w-8 text-blue-500 flex-shrink-0 mt-1\" />\n        <div className=\"flex-1 min-w-0\">\n          <p className=\"text-sm font-medium text-gray-900 truncate\">\n            {uploadedFile.file.name}\n          </p>\n          <p className=\"text-xs text-gray-500\">\n            {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB\n          </p>\n          <div className=\"mt-2\">\n            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(uploadedFile.status)}`}>\n              {getStatusText(uploadedFile.status)}\n            </span>\n          </div>\n          {uploadedFile.error && (\n            <p className=\"text-xs text-red-600 mt-1\">{uploadedFile.error}</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAqBe,SAAS,iBAAiB,KAMjB;QANiB,EACvC,aAAa,EACb,SAAS,EACT,YAAY,EACZ,aAAa,EACb,YAAY,EACU,GANiB;;IAOvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC,eAAuB;YACjD,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,SAAS;gBACT;YACF;YAEA,mBAAmB;YACnB,MAAM,iBAAiB,cAAc,MAAM;uEAAC,CAAA,OAAQ,KAAK,IAAI,GAAG,KAAK,OAAO;;YAC5E,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,SAAS;gBACT;YACF;YAEA,8BAA8B;YAC9B,IAAI,cAAc,MAAM,GAAG,cAAc,MAAM,GAAG,GAAG;gBACnD,SAAS;gBACT;YACF;YAEA,cAAc;QAChB;+CAAG;QAAC;QAAe,cAAc,MAAM;KAAC;IAExC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;QAC7B;QACA,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,aAAa,cAAc,MAAM,IAAI,KAAK,cAAc,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAEtF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,AAAC,uHAMR,OAJA,eACE,+BACA,yCACH,gBACqD,OAApD,eAAe,kCAAkC,IAAG;;kCAGxD,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCAAI,WAAU;;;;;qDAEf,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAGpB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,eACG,uBACA,eACE,4BACA;;;;;;oCAGP,CAAC,8BACA,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;YAMzB,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;YAK3C,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAsC;4BACjC,cAAc,MAAM;4BAAC;;;;;;;kCAGxC,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;gCAEC,cAAc;gCACd,UAAU,IAAM,aAAa,aAAa,EAAE;+BAFvC,aAAa,EAAE;;;;;;;;;;kCAQ1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,cAAc;4BACzB,WAAW,AAAC,qHAKT,OAHC,cAAc,CAAC,eACb,6CACA,gDACH;;8CAGH,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;8CACE,eACG,kBACA,aACE,AAAC,WAA+B,OAArB,cAAc,MAAM,EAAC,WAChC,AAAC,UAAmF,OAA1E,IAAI,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,EAAC,aAAuF,OAA5E,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM,KAAK,IAAI,KAAK,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStL;GAhJwB;;QAiCgC,2KAAA,CAAA,cAAW;;;KAjC3C;AAkJxB,sBAAsB;AACtB,SAAS,SAAS,KAAgF;QAAhF,EAAE,YAAY,EAAE,QAAQ,EAAwD,GAAhF;IAChB,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,aAAa,IAAI,CAAC,IAAI;;;;;;0CAEzB,6LAAC;gCAAE,WAAU;;oCACV,CAAC,aAAa,IAAI,CAAC,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAErD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAW,AAAC,0DAA6F,OAApC,eAAe,aAAa,MAAM;8CAC1G,cAAc,aAAa,MAAM;;;;;;;;;;;4BAGrC,aAAa,KAAK,kBACjB,6LAAC;gCAAE,WAAU;0CAA6B,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;AAMxE;MAjDS", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/ComparisonResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { PDFComparison, ComparisonDifference } from '@/types/pdf';\nimport { \n  GitCompare, \n  FileText, \n  AlertTriangle, \n  CheckCircle, \n  TrendingUp, \n  TrendingDown,\n  Minus,\n  Plus,\n  DollarSign,\n  Calendar,\n  Building,\n  FileCheck,\n  ChevronDown,\n  ChevronRight,\n  Copy\n} from 'lucide-react';\n\ninterface ComparisonResultsProps {\n  comparison: PDFComparison;\n  onExport: (format: 'json' | 'csv' | 'txt') => void;\n}\n\nexport default function ComparisonResults({ comparison, onExport }: ComparisonResultsProps) {\n  const [activeTab, setActiveTab] = useState<'overview' | 'differences' | 'documents'>('overview');\n  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({\n    pricing: true,\n    terms: true,\n    vendors: false,\n    timeline: false\n  });\n\n  const toggleSection = (section: string) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  const getDifferenceIcon = (type: string) => {\n    switch (type) {\n      case 'added': return <Plus className=\"h-4 w-4 text-green-500\" />;\n      case 'removed': return <Minus className=\"h-4 w-4 text-red-500\" />;\n      case 'changed': return <GitCompare className=\"h-4 w-4 text-blue-500\" />;\n      default: return <CheckCircle className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getSignificanceColor = (significance: string) => {\n    switch (significance) {\n      case 'high': return 'bg-red-100 text-red-800 border-red-200';\n      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';\n      case 'low': return 'bg-green-100 text-green-800 border-green-200';\n      default: return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n\n  const groupDifferencesByField = () => {\n    const groups: Record<string, ComparisonDifference[]> = {};\n    comparison.differences.forEach(diff => {\n      const category = diff.field.includes('pricing') || diff.field.includes('cost') || diff.field.includes('total') ? 'pricing' :\n                      diff.field.includes('terms') || diff.field.includes('condition') ? 'terms' :\n                      diff.field.includes('vendor') || diff.field.includes('supplier') ? 'vendors' :\n                      diff.field.includes('date') || diff.field.includes('timeline') ? 'timeline' : 'other';\n      \n      if (!groups[category]) groups[category] = [];\n      groups[category].push(diff);\n    });\n    return groups;\n  };\n\n  const groupedDifferences = groupDifferencesByField();\n\n  const tabs = [\n    { id: 'overview', label: 'Overview', icon: GitCompare },\n    { id: 'differences', label: 'Differences', icon: AlertTriangle },\n    { id: 'documents', label: 'Documents', icon: FileText }\n  ];\n\n  return (\n    <div className=\"w-full max-w-7xl mx-auto bg-white rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-6\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">\n              <GitCompare className=\"h-6 w-6 text-blue-600\" />\n              <span>PDF Comparison Results</span>\n            </h2>\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Comparing {comparison.documents.length} documents • {new Date(comparison.comparedAt).toLocaleString()}\n            </p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => onExport('json')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              <Copy className=\"h-4 w-4\" />\n              <span>JSON</span>\n            </button>\n            <button\n              onClick={() => onExport('csv')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors\"\n            >\n              <Copy className=\"h-4 w-4\" />\n              <span>CSV</span>\n            </button>\n            <button\n              onClick={() => onExport('txt')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n            >\n              <Copy className=\"h-4 w-4\" />\n              <span>TXT</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`\n                  flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors\n                  ${activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                  }\n                `}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n                {tab.id === 'differences' && (\n                  <span className=\"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full\">\n                    {comparison.differences.length}\n                  </span>\n                )}\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        {activeTab === 'overview' && (\n          <div className=\"space-y-6\">\n            {/* Summary */}\n            <div className=\"bg-blue-50 rounded-lg p-6 border border-blue-200\">\n              <h3 className=\"text-lg font-semibold text-blue-900 mb-3\">Comparison Summary</h3>\n              <p className=\"text-blue-800\">{comparison.summary}</p>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n              <StatCard\n                icon={FileText}\n                label=\"Documents\"\n                value={comparison.documents.length.toString()}\n                color=\"blue\"\n              />\n              <StatCard\n                icon={AlertTriangle}\n                label=\"Differences\"\n                value={comparison.differences.length.toString()}\n                color=\"red\"\n              />\n              <StatCard\n                icon={TrendingUp}\n                label=\"High Impact\"\n                value={comparison.differences.filter(d => d.significance === 'high').length.toString()}\n                color=\"orange\"\n              />\n              <StatCard\n                icon={CheckCircle}\n                label=\"Type\"\n                value={comparison.comparisonType}\n                color=\"green\"\n              />\n            </div>\n\n            {/* Recommendations */}\n            {comparison.recommendations.length > 0 && (\n              <div className=\"bg-green-50 rounded-lg p-6 border border-green-200\">\n                <h3 className=\"text-lg font-semibold text-green-900 mb-3\">Recommendations</h3>\n                <ul className=\"space-y-2\">\n                  {comparison.recommendations.map((rec, index) => (\n                    <li key={index} className=\"flex items-start space-x-2\">\n                      <CheckCircle className=\"h-5 w-5 text-green-600 mt-0.5 flex-shrink-0\" />\n                      <span className=\"text-green-800\">{rec}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'differences' && (\n          <div className=\"space-y-6\">\n            {Object.entries(groupedDifferences).map(([category, diffs]) => (\n              <DifferenceSection\n                key={category}\n                title={category.charAt(0).toUpperCase() + category.slice(1)}\n                differences={diffs}\n                isExpanded={expandedSections[category]}\n                onToggle={() => toggleSection(category)}\n                getDifferenceIcon={getDifferenceIcon}\n                getSignificanceColor={getSignificanceColor}\n              />\n            ))}\n          </div>\n        )}\n\n        {activeTab === 'documents' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {comparison.documents.map((doc, index) => (\n              <DocumentCard key={doc.id} document={doc} index={index} />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// Helper Components\nfunction StatCard({ icon: Icon, label, value, color }: { icon: any, label: string, value: string, color: string }) {\n  const colorClasses = {\n    blue: 'bg-blue-50 text-blue-600 border-blue-200',\n    red: 'bg-red-50 text-red-600 border-red-200',\n    orange: 'bg-orange-50 text-orange-600 border-orange-200',\n    green: 'bg-green-50 text-green-600 border-green-200'\n  };\n\n  return (\n    <div className={`border rounded-lg p-4 ${colorClasses[color as keyof typeof colorClasses]}`}>\n      <div className=\"flex items-center space-x-2\">\n        <Icon className=\"h-5 w-5\" />\n        <span className=\"text-sm font-medium\">{label}</span>\n      </div>\n      <p className=\"text-2xl font-bold mt-2\">{value}</p>\n    </div>\n  );\n}\n\nfunction DifferenceSection({ \n  title, \n  differences, \n  isExpanded, \n  onToggle, \n  getDifferenceIcon, \n  getSignificanceColor \n}: {\n  title: string,\n  differences: ComparisonDifference[],\n  isExpanded: boolean,\n  onToggle: () => void,\n  getDifferenceIcon: (type: string) => React.ReactNode,\n  getSignificanceColor: (significance: string) => string\n}) {\n  if (differences.length === 0) return null;\n\n  return (\n    <div className=\"border rounded-lg\">\n      <button\n        onClick={onToggle}\n        className=\"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\"\n      >\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title} ({differences.length})</h3>\n        {isExpanded ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n      </button>\n      {isExpanded && (\n        <div className=\"p-4 border-t space-y-3\">\n          {differences.map((diff, index) => (\n            <div key={index} className=\"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\">\n              {getDifferenceIcon(diff.type)}\n              <div className=\"flex-1\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                  <span className=\"font-medium text-gray-900\">{diff.field.replace(/_/g, ' ')}</span>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getSignificanceColor(diff.significance)}`}>\n                    {diff.significance}\n                  </span>\n                </div>\n                <p className=\"text-sm text-gray-700 mb-2\">{diff.description}</p>\n                {(diff.oldValue || diff.newValue) && (\n                  <div className=\"text-xs space-y-1\">\n                    {diff.oldValue && (\n                      <div className=\"text-red-600\">\n                        <span className=\"font-medium\">Before:</span> {diff.oldValue}\n                      </div>\n                    )}\n                    {diff.newValue && (\n                      <div className=\"text-green-600\">\n                        <span className=\"font-medium\">After:</span> {diff.newValue}\n                      </div>\n                    )}\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction DocumentCard({ document, index }: { document: any, index: number }) {\n  return (\n    <div className=\"border rounded-lg p-4\">\n      <div className=\"flex items-center space-x-2 mb-3\">\n        <FileText className=\"h-5 w-5 text-blue-500\" />\n        <h3 className=\"font-semibold text-gray-900\">Document {index + 1}</h3>\n      </div>\n      <div className=\"space-y-2 text-sm\">\n        <p><span className=\"font-medium\">Name:</span> {document.fileName}</p>\n        <p><span className=\"font-medium\">Type:</span> {document.aiAnalysis?.documentType || 'Unknown'}</p>\n        <p><span className=\"font-medium\">Pages:</span> {document.statistics.totalPages}</p>\n        <p><span className=\"font-medium\">Words:</span> {document.statistics.totalWords.toLocaleString()}</p>\n        {document.aiAnalysis?.quotationDetails?.totals?.total && (\n          <p><span className=\"font-medium\">Total:</span> {document.aiAnalysis.quotationDetails.totals.total} {document.aiAnalysis.quotationDetails.totals.currency}</p>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;AA2Be,SAAS,kBAAkB,KAAgD;QAAhD,EAAE,UAAU,EAAE,QAAQ,EAA0B,GAAhD;;IACxC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4C;IACrF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAChF,SAAS;QACT,OAAO;QACP,SAAS;QACT,UAAU;IACZ;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAS,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACrC,KAAK;gBAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAW,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC7C;gBAAS,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QACzC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,0BAA0B;QAC9B,MAAM,SAAiD,CAAC;QACxD,WAAW,WAAW,CAAC,OAAO,CAAC,CAAA;YAC7B,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ,CAAC,cAAc,KAAK,KAAK,CAAC,QAAQ,CAAC,WAAW,KAAK,KAAK,CAAC,QAAQ,CAAC,WAAW,YACjG,KAAK,KAAK,CAAC,QAAQ,CAAC,YAAY,KAAK,KAAK,CAAC,QAAQ,CAAC,eAAe,UACnE,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa,KAAK,KAAK,CAAC,QAAQ,CAAC,cAAc,YACnE,KAAK,KAAK,CAAC,QAAQ,CAAC,WAAW,KAAK,KAAK,CAAC,QAAQ,CAAC,cAAc,aAAa;YAE9F,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE;YAC5C,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC;QACxB;QACA,OAAO;IACT;IAEA,MAAM,qBAAqB;IAE3B,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;QACtD;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM,2NAAA,CAAA,gBAAa;QAAC;QAC/D;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,iNAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAE,WAAU;;wCAA6B;wCAC7B,WAAW,SAAS,CAAC,MAAM;wCAAC;wCAAc,IAAI,KAAK,WAAW,UAAU,EAAE,cAAc;;;;;;;;;;;;;sCAGvG,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,qBACE,6LAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,AAAC,4HAKT,OAHC,cAAc,IAAI,EAAE,GAClB,kCACA,wDACH;;8CAGH,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,IAAI,KAAK;;;;;;gCACf,IAAI,EAAE,KAAK,+BACV,6LAAC;oCAAK,WAAU;8CACb,WAAW,WAAW,CAAC,MAAM;;;;;;;2BAd7B,IAAI,EAAE;;;;;oBAmBjB;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAiB,WAAW,OAAO;;;;;;;;;;;;0CAIlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAM,iNAAA,CAAA,WAAQ;wCACd,OAAM;wCACN,OAAO,WAAW,SAAS,CAAC,MAAM,CAAC,QAAQ;wCAC3C,OAAM;;;;;;kDAER,6LAAC;wCACC,MAAM,2NAAA,CAAA,gBAAa;wCACnB,OAAM;wCACN,OAAO,WAAW,WAAW,CAAC,MAAM,CAAC,QAAQ;wCAC7C,OAAM;;;;;;kDAER,6LAAC;wCACC,MAAM,qNAAA,CAAA,aAAU;wCAChB,OAAM;wCACN,OAAO,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,QAAQ,MAAM,CAAC,QAAQ;wCACpF,OAAM;;;;;;kDAER,6LAAC;wCACC,MAAM,8NAAA,CAAA,cAAW;wCACjB,OAAM;wCACN,OAAO,WAAW,cAAc;wCAChC,OAAM;;;;;;;;;;;;4BAKT,WAAW,eAAe,CAAC,MAAM,GAAG,mBACnC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,6LAAC;wCAAG,WAAU;kDACX,WAAW,eAAe,CAAC,GAAG,CAAC,CAAC,KAAK,sBACpC,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAK,WAAU;kEAAkB;;;;;;;+CAF3B;;;;;;;;;;;;;;;;;;;;;;oBAWpB,cAAc,+BACb,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,oBAAoB,GAAG,CAAC;gCAAC,CAAC,UAAU,MAAM;iDACxD,6LAAC;gCAEC,OAAO,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;gCACzD,aAAa;gCACb,YAAY,gBAAgB,CAAC,SAAS;gCACtC,UAAU,IAAM,cAAc;gCAC9B,mBAAmB;gCACnB,sBAAsB;+BANjB;;;;;;;;;;;oBAYZ,cAAc,6BACb,6LAAC;wBAAI,WAAU;kCACZ,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC9B,6LAAC;gCAA0B,UAAU;gCAAK,OAAO;+BAA9B,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;AAOvC;GA/MwB;KAAA;AAiNxB,oBAAoB;AACpB,SAAS,SAAS,KAA+F;QAA/F,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAA8D,GAA/F;IAChB,MAAM,eAAe;QACnB,MAAM;QACN,KAAK;QACL,QAAQ;QACR,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,yBAAyE,OAAjD,YAAY,CAAC,MAAmC;;0BACvF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAuB;;;;;;;;;;;;0BAEzC,6LAAC;gBAAE,WAAU;0BAA2B;;;;;;;;;;;;AAG9C;MAjBS;AAmBT,SAAS,kBAAkB,KAc1B;QAd0B,EACzB,KAAK,EACL,WAAW,EACX,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,oBAAoB,EAQrB,GAd0B;IAezB,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO;IAErC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;;4BAAuC;4BAAM;4BAAG,YAAY,MAAM;4BAAC;;;;;;;oBAChF,2BAAa,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;6CAAe,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;YAE7E,4BACC,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;wBAAgB,WAAU;;4BACxB,kBAAkB,KAAK,IAAI;0CAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA6B,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM;;;;;;0DACtE,6LAAC;gDAAK,WAAW,AAAC,qDAA4F,OAAxC,qBAAqB,KAAK,YAAY;0DACzG,KAAK,YAAY;;;;;;;;;;;;kDAGtB,6LAAC;wCAAE,WAAU;kDAA8B,KAAK,WAAW;;;;;;oCAC1D,CAAC,KAAK,QAAQ,IAAI,KAAK,QAAQ,mBAC9B,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,QAAQ,kBACZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAc;oDAAE,KAAK,QAAQ;;;;;;;4CAG9D,KAAK,QAAQ,kBACZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAc;;;;;;oDAAa;oDAAE,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;uBAnB5D;;;;;;;;;;;;;;;;AA+BtB;MA5DS;AA8DT,SAAS,aAAa,KAAqD;QAArD,EAAE,QAAQ,EAAE,KAAK,EAAoC,GAArD;QASiC,sBAG9C,8CAAA,uCAAA;IAXP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;;4BAA8B;4BAAU,QAAQ;;;;;;;;;;;;;0BAEhE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CAAE,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAY;4BAAE,SAAS,QAAQ;;;;;;;kCAChE,6LAAC;;0CAAE,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAY;4BAAE,EAAA,uBAAA,SAAS,UAAU,cAAnB,2CAAA,qBAAqB,YAAY,KAAI;;;;;;;kCACpF,6LAAC;;0CAAE,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAa;4BAAE,SAAS,UAAU,CAAC,UAAU;;;;;;;kCAC9E,6LAAC;;0CAAE,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAa;4BAAE,SAAS,UAAU,CAAC,UAAU,CAAC,cAAc;;;;;;;oBAC5F,EAAA,wBAAA,SAAS,UAAU,cAAnB,6CAAA,wCAAA,sBAAqB,gBAAgB,cAArC,6DAAA,+CAAA,sCAAuC,MAAM,cAA7C,mEAAA,6CAA+C,KAAK,mBACnD,6LAAC;;0CAAE,6LAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAa;4BAAE,SAAS,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK;4BAAC;4BAAE,SAAS,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;AAKlK;MAlBS", "debugId": null}}, {"offset": {"line": 1253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/pdfExtractor.ts"], "sourcesContent": ["import { ExtractedPDFData } from '@/types/pdf';\n\nexport class PDFExtractorService {\n  static async extractPDFData(file: File): Promise<ExtractedPDFData> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await fetch('/api/extract-pdf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to extract PDF data');\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error('PDF extraction error:', error);\n      throw new Error(error instanceof Error ? error.message : 'Failed to extract PDF data. Please ensure the file is a valid PDF.');\n    }\n  }\n\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,aAAa,eAAe,IAAU,EAA6B;QACjE,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3D;IACF;AAEF", "debugId": null}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport MultiPDFUploader from '@/components/MultiPDFUploader';\nimport ComparisonResults from '@/components/ComparisonResults';\nimport { PDFExtractorService } from '@/services/pdfExtractor';\nimport { ExportService } from '@/services/exportService';\nimport { ExtractedPDFData, PDFComparison } from '@/types/pdf';\nimport { GitCompare, Zap, Shield, Download, FileText } from 'lucide-react';\n\ninterface UploadedFile {\n  id: string;\n  file: File;\n  status: 'pending' | 'processing' | 'completed' | 'error';\n  data?: ExtractedPDFData;\n  error?: string;\n}\n\nexport default function Home() {\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\n  const [comparisonResult, setComparisonResult] = useState<PDFComparison | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleFilesUpload = async (files: File[]) => {\n    const newFiles: UploadedFile[] = files.map(file => ({\n      id: Date.now().toString(36) + Math.random().toString(36).substr(2),\n      file,\n      status: 'pending' as const\n    }));\n\n    setUploadedFiles(prev => [...prev, ...newFiles]);\n    setError(null);\n\n    // Process each file\n    for (const uploadedFile of newFiles) {\n      try {\n        setUploadedFiles(prev => prev.map(f =>\n          f.id === uploadedFile.id ? { ...f, status: 'processing' } : f\n        ));\n\n        const data = await PDFExtractorService.extractPDFData(uploadedFile.file);\n\n        setUploadedFiles(prev => prev.map(f =>\n          f.id === uploadedFile.id ? { ...f, status: 'completed', data } : f\n        ));\n      } catch (err) {\n        const errorMessage = err instanceof Error ? err.message : 'Failed to process PDF';\n        setUploadedFiles(prev => prev.map(f =>\n          f.id === uploadedFile.id ? { ...f, status: 'error', error: errorMessage } : f\n        ));\n      }\n    }\n  };\n\n  const handleCompare = async () => {\n    const completedFiles = uploadedFiles.filter(f => f.status === 'completed' && f.data);\n\n    if (completedFiles.length < 2) {\n      setError('At least 2 successfully processed PDFs are required for comparison');\n      return;\n    }\n\n    setIsProcessing(true);\n    setError(null);\n\n    try {\n      const documents = completedFiles.map(f => f.data!);\n\n      const response = await fetch('/api/compare-pdfs', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ documents }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to compare PDFs');\n      }\n\n      const comparison = await response.json();\n      setComparisonResult(comparison);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred while comparing PDFs');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleRemoveFile = (id: string) => {\n    setUploadedFiles(prev => prev.filter(f => f.id !== id));\n  };\n\n  const handleExport = (format: 'json' | 'csv' | 'txt') => {\n    if (!comparisonResult) return;\n\n    const exportData = {\n      comparison: comparisonResult,\n      exportedAt: new Date().toISOString()\n    };\n\n    switch (format) {\n      case 'json':\n        const jsonString = JSON.stringify(exportData, null, 2);\n        downloadFile(jsonString, `comparison_${comparisonResult.id}.json`, 'application/json');\n        break;\n      case 'csv':\n        const csvContent = convertComparisonToCSV(comparisonResult);\n        downloadFile(csvContent, `comparison_${comparisonResult.id}.csv`, 'text/csv');\n        break;\n      case 'txt':\n        const textContent = convertComparisonToText(comparisonResult);\n        downloadFile(textContent, `comparison_${comparisonResult.id}.txt`, 'text/plain');\n        break;\n    }\n  };\n\n  const resetApp = () => {\n    setUploadedFiles([]);\n    setComparisonResult(null);\n    setError(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"bg-blue-600 p-2 rounded-lg\">\n                <GitCompare className=\"h-8 w-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">PDF Comparison Tool</h1>\n                <p className=\"text-gray-600\">Compare multiple PDF documents and analyze differences</p>\n              </div>\n            </div>\n            {(uploadedFiles.length > 0 || comparisonResult) && (\n              <button\n                onClick={resetApp}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n              >\n                New Comparison\n              </button>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!comparisonResult && (\n          <>\n            {/* Features Section */}\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                AI-Powered PDF Comparison Tool\n              </h2>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-3xl mx-auto\">\n                Upload multiple PDF documents to compare quotations, contracts, proposals, and identify\n                key differences in pricing, terms, vendors, and timelines with AI analysis.\n              </p>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n                <FeatureCard\n                  icon={GitCompare}\n                  title=\"Smart Comparison\"\n                  description=\"AI-powered analysis identifies differences in pricing, terms, vendors, and timelines\"\n                />\n                <FeatureCard\n                  icon={Shield}\n                  title=\"Secure & Private\"\n                  description=\"All processing happens securely on our servers. Your files are processed and deleted immediately\"\n                />\n                <FeatureCard\n                  icon={Download}\n                  title=\"Detailed Reports\"\n                  description=\"Export comprehensive comparison reports in JSON, CSV, or formatted text\"\n                />\n              </div>\n            </div>\n\n            {/* Upload Section */}\n            <MultiPDFUploader\n              onFilesUpload={handleFilesUpload}\n              onCompare={handleCompare}\n              isProcessing={isProcessing}\n              uploadedFiles={uploadedFiles}\n              onRemoveFile={handleRemoveFile}\n            />\n          </>\n        )}\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"max-w-4xl mx-auto mb-8\">\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"flex\">\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                  <div className=\"mt-2 text-sm text-red-700\">\n                    <p>{error}</p>\n                  </div>\n                  <div className=\"mt-4\">\n                    <button\n                      onClick={() => setError(null)}\n                      className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 transition-colors\"\n                    >\n                      Dismiss\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Results */}\n        {comparisonResult && (\n          <ComparisonResults comparison={comparisonResult} onExport={handleExport} />\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>Built with Next.js, TypeScript, and Tailwind CSS</p>\n            <p className=\"mt-2 text-sm\">AI-powered PDF comparison with OpenAI integration</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\n// Helper functions\nfunction downloadFile(content: string, filename: string, mimeType: string): void {\n  const blob = new Blob([content], { type: mimeType });\n  const url = URL.createObjectURL(blob);\n\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n\n  URL.revokeObjectURL(url);\n}\n\nfunction convertComparisonToCSV(comparison: PDFComparison): string {\n  const rows: string[] = [];\n\n  // Header\n  rows.push('Category,Field,Type,Significance,Description,Old Value,New Value');\n\n  // Differences\n  comparison.differences.forEach(diff => {\n    rows.push([\n      'Difference',\n      diff.field,\n      diff.type,\n      diff.significance,\n      `\"${diff.description.replace(/\"/g, '\"\"')}\"`,\n      `\"${diff.oldValue || ''}\"`,\n      `\"${diff.newValue || ''}\"`\n    ].join(','));\n  });\n\n  // Documents\n  comparison.documents.forEach((doc, index) => {\n    rows.push([\n      'Document',\n      `Document ${index + 1}`,\n      'info',\n      'low',\n      `\"${doc.fileName}\"`,\n      `\"${doc.statistics.totalPages} pages\"`,\n      `\"${doc.statistics.totalWords} words\"`\n    ].join(','));\n  });\n\n  return rows.join('\\n');\n}\n\nfunction convertComparisonToText(comparison: PDFComparison): string {\n  const sections: string[] = [];\n\n  // Header\n  sections.push('PDF COMPARISON REPORT');\n  sections.push('='.repeat(50));\n  sections.push(`Comparison ID: ${comparison.id}`);\n  sections.push(`Compared: ${new Date(comparison.comparedAt).toLocaleString()}`);\n  sections.push(`Type: ${comparison.comparisonType}`);\n  sections.push('');\n\n  // Summary\n  sections.push('SUMMARY');\n  sections.push('-'.repeat(20));\n  sections.push(comparison.summary);\n  sections.push('');\n\n  // Documents\n  sections.push('DOCUMENTS');\n  sections.push('-'.repeat(20));\n  comparison.documents.forEach((doc, index) => {\n    sections.push(`${index + 1}. ${doc.fileName}`);\n    sections.push(`   Type: ${doc.aiAnalysis?.documentType || 'Unknown'}`);\n    sections.push(`   Pages: ${doc.statistics.totalPages}`);\n    sections.push(`   Words: ${doc.statistics.totalWords.toLocaleString()}`);\n    sections.push('');\n  });\n\n  // Differences\n  sections.push('DIFFERENCES');\n  sections.push('-'.repeat(20));\n  comparison.differences.forEach((diff, index) => {\n    sections.push(`${index + 1}. ${diff.field.replace(/_/g, ' ').toUpperCase()} [${diff.significance.toUpperCase()}]`);\n    sections.push(`   Type: ${diff.type}`);\n    sections.push(`   Description: ${diff.description}`);\n    if (diff.oldValue) sections.push(`   Before: ${diff.oldValue}`);\n    if (diff.newValue) sections.push(`   After: ${diff.newValue}`);\n    sections.push('');\n  });\n\n  // Recommendations\n  if (comparison.recommendations.length > 0) {\n    sections.push('RECOMMENDATIONS');\n    sections.push('-'.repeat(20));\n    comparison.recommendations.forEach((rec, index) => {\n      sections.push(`${index + 1}. ${rec}`);\n    });\n  }\n\n  return sections.join('\\n');\n}\n\n// Feature Card Component\nfunction FeatureCard({\n  icon: Icon,\n  title,\n  description\n}: {\n  icon: any,\n  title: string,\n  description: string\n}) {\n  return (\n    <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n      <div className=\"bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4\">\n        <Icon className=\"h-6 w-6 text-blue-600\" />\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 text-sm\">{description}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AAAA;AAAA;;;AARA;;;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB,OAAO;QAC/B,MAAM,WAA2B,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAClD,IAAI,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;gBAChE;gBACA,QAAQ;YACV,CAAC;QAED,iBAAiB,CAAA,OAAQ;mBAAI;mBAAS;aAAS;QAC/C,SAAS;QAET,oBAAoB;QACpB,KAAK,MAAM,gBAAgB,SAAU;YACnC,IAAI;gBACF,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAa,IAAI;gBAG9D,MAAM,OAAO,MAAM,kIAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,aAAa,IAAI;gBAEvE,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;4BAAa;wBAAK,IAAI;YAErE,EAAE,OAAO,KAAK;gBACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC1D,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;4BAAS,OAAO;wBAAa,IAAI;YAEhF;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,EAAE,IAAI;QAEnF,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,MAAM,YAAY,eAAe,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;YAEhD,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAU;YACnC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,aAAa,MAAM,SAAS,IAAI;YACtC,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,kBAAkB;QAEvB,MAAM,aAAa;YACjB,YAAY;YACZ,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,OAAQ;YACN,KAAK;gBACH,MAAM,aAAa,KAAK,SAAS,CAAC,YAAY,MAAM;gBACpD,aAAa,YAAY,AAAC,cAAiC,OAApB,iBAAiB,EAAE,EAAC,UAAQ;gBACnE;YACF,KAAK;gBACH,MAAM,aAAa,uBAAuB;gBAC1C,aAAa,YAAY,AAAC,cAAiC,OAApB,iBAAiB,EAAE,EAAC,SAAO;gBAClE;YACF,KAAK;gBACH,MAAM,cAAc,wBAAwB;gBAC5C,aAAa,aAAa,AAAC,cAAiC,OAApB,iBAAiB,EAAE,EAAC,SAAO;gBACnE;QACJ;IACF;IAEA,MAAM,WAAW;QACf,iBAAiB,EAAE;QACnB,oBAAoB;QACpB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;4BAGhC,CAAC,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC5C,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;;oBACb,CAAC,kCACA;;0CAEE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAK5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAM,qNAAA,CAAA,aAAU;gDAChB,OAAM;gDACN,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAM,yMAAA,CAAA,SAAM;gDACZ,OAAM;gDACN,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAM,6MAAA,CAAA,WAAQ;gDACd,OAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;;0CAMlB,6LAAC,yIAAA,CAAA,UAAgB;gCACf,eAAe;gCACf,WAAW;gCACX,cAAc;gCACd,eAAe;gCACf,cAAc;;;;;;;;oBAMnB,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAG;;;;;;;;;;;sDAEN,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS,IAAM,SAAS;gDACxB,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWZ,kCACC,6LAAC,0IAAA,CAAA,UAAiB;wBAAC,YAAY;wBAAkB,UAAU;;;;;;;;;;;;0BAK/D,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GA3NwB;KAAA;AA6NxB,mBAAmB;AACnB,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB;IACvE,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAS;IAClD,MAAM,MAAM,IAAI,eAAe,CAAC;IAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,IAAI,eAAe,CAAC;AACtB;AAEA,SAAS,uBAAuB,UAAyB;IACvD,MAAM,OAAiB,EAAE;IAEzB,SAAS;IACT,KAAK,IAAI,CAAC;IAEV,cAAc;IACd,WAAW,WAAW,CAAC,OAAO,CAAC,CAAA;QAC7B,KAAK,IAAI,CAAC;YACR;YACA,KAAK,KAAK;YACV,KAAK,IAAI;YACT,KAAK,YAAY;YAChB,IAAwC,OAArC,KAAK,WAAW,CAAC,OAAO,CAAC,MAAM,OAAM;YACxC,IAAuB,OAApB,KAAK,QAAQ,IAAI,IAAG;YACvB,IAAuB,OAApB,KAAK,QAAQ,IAAI,IAAG;SACzB,CAAC,IAAI,CAAC;IACT;IAEA,YAAY;IACZ,WAAW,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK;QACjC,KAAK,IAAI,CAAC;YACR;YACC,YAAqB,OAAV,QAAQ;YACpB;YACA;YACC,IAAgB,OAAb,IAAI,QAAQ,EAAC;YAChB,IAA6B,OAA1B,IAAI,UAAU,CAAC,UAAU,EAAC;YAC7B,IAA6B,OAA1B,IAAI,UAAU,CAAC,UAAU,EAAC;SAC/B,CAAC,IAAI,CAAC;IACT;IAEA,OAAO,KAAK,IAAI,CAAC;AACnB;AAEA,SAAS,wBAAwB,UAAyB;IACxD,MAAM,WAAqB,EAAE;IAE7B,SAAS;IACT,SAAS,IAAI,CAAC;IACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;IACzB,SAAS,IAAI,CAAC,AAAC,kBAA+B,OAAd,WAAW,EAAE;IAC7C,SAAS,IAAI,CAAC,AAAC,aAA6D,OAAjD,IAAI,KAAK,WAAW,UAAU,EAAE,cAAc;IACzE,SAAS,IAAI,CAAC,AAAC,SAAkC,OAA1B,WAAW,cAAc;IAChD,SAAS,IAAI,CAAC;IAEd,UAAU;IACV,SAAS,IAAI,CAAC;IACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;IACzB,SAAS,IAAI,CAAC,WAAW,OAAO;IAChC,SAAS,IAAI,CAAC;IAEd,YAAY;IACZ,SAAS,IAAI,CAAC;IACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;IACzB,WAAW,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK;YAEP;QAD1B,SAAS,IAAI,CAAC,AAAC,GAAgB,OAAd,QAAQ,GAAE,MAAiB,OAAb,IAAI,QAAQ;QAC3C,SAAS,IAAI,CAAC,AAAC,YAAqD,OAA1C,EAAA,kBAAA,IAAI,UAAU,cAAd,sCAAA,gBAAgB,YAAY,KAAI;QAC1D,SAAS,IAAI,CAAC,AAAC,aAAsC,OAA1B,IAAI,UAAU,CAAC,UAAU;QACpD,SAAS,IAAI,CAAC,AAAC,aAAuD,OAA3C,IAAI,UAAU,CAAC,UAAU,CAAC,cAAc;QACnE,SAAS,IAAI,CAAC;IAChB;IAEA,cAAc;IACd,SAAS,IAAI,CAAC;IACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;IACzB,WAAW,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;QACpC,SAAS,IAAI,CAAC,AAAC,GAAgB,OAAd,QAAQ,GAAE,MAAoD,OAAhD,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,IAAG,MAAoC,OAAhC,KAAK,YAAY,CAAC,WAAW,IAAG;QAC/G,SAAS,IAAI,CAAC,AAAC,YAAqB,OAAV,KAAK,IAAI;QACnC,SAAS,IAAI,CAAC,AAAC,mBAAmC,OAAjB,KAAK,WAAW;QACjD,IAAI,KAAK,QAAQ,EAAE,SAAS,IAAI,CAAC,AAAC,cAA2B,OAAd,KAAK,QAAQ;QAC5D,IAAI,KAAK,QAAQ,EAAE,SAAS,IAAI,CAAC,AAAC,aAA0B,OAAd,KAAK,QAAQ;QAC3D,SAAS,IAAI,CAAC;IAChB;IAEA,kBAAkB;IAClB,IAAI,WAAW,eAAe,CAAC,MAAM,GAAG,GAAG;QACzC,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,WAAW,eAAe,CAAC,OAAO,CAAC,CAAC,KAAK;YACvC,SAAS,IAAI,CAAC,AAAC,GAAgB,OAAd,QAAQ,GAAE,MAAQ,OAAJ;QACjC;IACF;IAEA,OAAO,SAAS,IAAI,CAAC;AACvB;AAEA,yBAAyB;AACzB,SAAS,YAAY,KAQpB;QARoB,EACnB,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EAKZ,GARoB;IASnB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C;MAlBS", "debugId": null}}]}