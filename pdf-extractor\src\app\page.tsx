'use client';

import React, { useState } from 'react';
import MultiPDFUploader from '@/components/MultiPDFUploader';
import ComparisonResults from '@/components/ComparisonResults';
import { PDFExtractorService } from '@/services/pdfExtractor';
import { ExportService } from '@/services/exportService';
import { ExtractedPDFData, PDFComparison } from '@/types/pdf';
import { GitCompare, Zap, Shield, Download, FileText } from 'lucide-react';

interface UploadedFile {
  id: string;
  file: File;
  status: 'pending' | 'processing' | 'completed' | 'error';
  data?: ExtractedPDFData;
  error?: string;
}

export default function Home() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [comparisonResult, setComparisonResult] = useState<PDFComparison | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFilesUpload = async (files: File[]) => {
    const newFiles: UploadedFile[] = files.map(file => ({
      id: Date.now().toString(36) + Math.random().toString(36).substr(2),
      file,
      status: 'pending' as const
    }));

    setUploadedFiles(prev => [...prev, ...newFiles]);
    setError(null);

    // Process each file
    for (const uploadedFile of newFiles) {
      try {
        setUploadedFiles(prev => prev.map(f =>
          f.id === uploadedFile.id ? { ...f, status: 'processing' } : f
        ));

        const data = await PDFExtractorService.extractPDFData(uploadedFile.file);

        setUploadedFiles(prev => prev.map(f =>
          f.id === uploadedFile.id ? { ...f, status: 'completed', data } : f
        ));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to process PDF';
        setUploadedFiles(prev => prev.map(f =>
          f.id === uploadedFile.id ? { ...f, status: 'error', error: errorMessage } : f
        ));
      }
    }
  };

  const handleCompare = async () => {
    const completedFiles = uploadedFiles.filter(f => f.status === 'completed' && f.data);

    if (completedFiles.length < 2) {
      setError('At least 2 successfully processed PDFs are required for comparison');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const documents = completedFiles.map(f => f.data!);

      const response = await fetch('/api/compare-pdfs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documents }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to compare PDFs');
      }

      const comparison = await response.json();
      setComparisonResult(comparison);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while comparing PDFs');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveFile = (id: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== id));
  };

  const handleExport = (format: 'json' | 'csv' | 'txt') => {
    if (!comparisonResult) return;

    const exportData = {
      comparison: comparisonResult,
      exportedAt: new Date().toISOString()
    };

    switch (format) {
      case 'json':
        const jsonString = JSON.stringify(exportData, null, 2);
        downloadFile(jsonString, `comparison_${comparisonResult.id}.json`, 'application/json');
        break;
      case 'csv':
        const csvContent = convertComparisonToCSV(comparisonResult);
        downloadFile(csvContent, `comparison_${comparisonResult.id}.csv`, 'text/csv');
        break;
      case 'txt':
        const textContent = convertComparisonToText(comparisonResult);
        downloadFile(textContent, `comparison_${comparisonResult.id}.txt`, 'text/plain');
        break;
    }
  };

  const resetApp = () => {
    setUploadedFiles([]);
    setComparisonResult(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <GitCompare className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">PDF Comparison Tool</h1>
                <p className="text-gray-600">Compare multiple PDF documents and analyze differences</p>
              </div>
            </div>
            {(uploadedFiles.length > 0 || comparisonResult) && (
              <button
                onClick={resetApp}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                New Comparison
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!comparisonResult && (
          <>
            {/* Features Section */}
            <div className="text-center mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                AI-Powered PDF Comparison Tool
              </h2>
              <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto">
                Upload multiple PDF documents to compare quotations, contracts, proposals, and identify
                key differences in pricing, terms, vendors, and timelines with AI analysis.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <FeatureCard
                  icon={GitCompare}
                  title="Smart Comparison"
                  description="AI-powered analysis identifies differences in pricing, terms, vendors, and timelines"
                />
                <FeatureCard
                  icon={Shield}
                  title="Secure & Private"
                  description="All processing happens securely on our servers. Your files are processed and deleted immediately"
                />
                <FeatureCard
                  icon={Download}
                  title="Detailed Reports"
                  description="Export comprehensive comparison reports in JSON, CSV, or formatted text"
                />
              </div>
            </div>

            {/* Upload Section */}
            <MultiPDFUploader
              onFilesUpload={handleFilesUpload}
              onCompare={handleCompare}
              isProcessing={isProcessing}
              uploadedFiles={uploadedFiles}
              onRemoveFile={handleRemoveFile}
            />
          </>
        )}

        {/* Error Display */}
        {error && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                  <div className="mt-4">
                    <button
                      onClick={() => setError(null)}
                      className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 transition-colors"
                    >
                      Dismiss
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        {comparisonResult && (
          <ComparisonResults comparison={comparisonResult} onExport={handleExport} />
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>Built with Next.js, TypeScript, and Tailwind CSS</p>
            <p className="mt-2 text-sm">AI-powered PDF comparison with OpenAI integration</p>
          </div>
        </div>
      </footer>
    </div>
  );
}

// Helper functions
function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
}

function convertComparisonToCSV(comparison: PDFComparison): string {
  const rows: string[] = [];

  // Header
  rows.push('Category,Field,Type,Significance,Description,Old Value,New Value');

  // Differences
  comparison.differences.forEach(diff => {
    rows.push([
      'Difference',
      diff.field,
      diff.type,
      diff.significance,
      `"${diff.description.replace(/"/g, '""')}"`,
      `"${diff.oldValue || ''}"`,
      `"${diff.newValue || ''}"`
    ].join(','));
  });

  // Documents
  comparison.documents.forEach((doc, index) => {
    rows.push([
      'Document',
      `Document ${index + 1}`,
      'info',
      'low',
      `"${doc.fileName}"`,
      `"${doc.statistics.totalPages} pages"`,
      `"${doc.statistics.totalWords} words"`
    ].join(','));
  });

  return rows.join('\n');
}

function convertComparisonToText(comparison: PDFComparison): string {
  const sections: string[] = [];

  // Header
  sections.push('PDF COMPARISON REPORT');
  sections.push('='.repeat(50));
  sections.push(`Comparison ID: ${comparison.id}`);
  sections.push(`Compared: ${new Date(comparison.comparedAt).toLocaleString()}`);
  sections.push(`Type: ${comparison.comparisonType}`);
  sections.push('');

  // Summary
  sections.push('SUMMARY');
  sections.push('-'.repeat(20));
  sections.push(comparison.summary);
  sections.push('');

  // Documents
  sections.push('DOCUMENTS');
  sections.push('-'.repeat(20));
  comparison.documents.forEach((doc, index) => {
    sections.push(`${index + 1}. ${doc.fileName}`);
    sections.push(`   Type: ${doc.aiAnalysis?.documentType || 'Unknown'}`);
    sections.push(`   Pages: ${doc.statistics.totalPages}`);
    sections.push(`   Words: ${doc.statistics.totalWords.toLocaleString()}`);
    sections.push('');
  });

  // Differences
  sections.push('DIFFERENCES');
  sections.push('-'.repeat(20));
  comparison.differences.forEach((diff, index) => {
    sections.push(`${index + 1}. ${diff.field.replace(/_/g, ' ').toUpperCase()} [${diff.significance.toUpperCase()}]`);
    sections.push(`   Type: ${diff.type}`);
    sections.push(`   Description: ${diff.description}`);
    if (diff.oldValue) sections.push(`   Before: ${diff.oldValue}`);
    if (diff.newValue) sections.push(`   After: ${diff.newValue}`);
    sections.push('');
  });

  // Recommendations
  if (comparison.recommendations.length > 0) {
    sections.push('RECOMMENDATIONS');
    sections.push('-'.repeat(20));
    comparison.recommendations.forEach((rec, index) => {
      sections.push(`${index + 1}. ${rec}`);
    });
  }

  return sections.join('\n');
}

// Feature Card Component
function FeatureCard({
  icon: Icon,
  title,
  description
}: {
  icon: any,
  title: string,
  description: string
}) {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border">
      <div className="bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4">
        <Icon className="h-6 w-6 text-blue-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 text-sm">{description}</p>
    </div>
  );
}
