'use client';

import React, { useState, useEffect } from 'react';
import { PDFComparison, ComparisonDifference, ComparisonAnalysis } from '@/types/pdf';
import SideBySideComparisonView from './SideBySideComparison';
import {
  GitCompare,
  FileText,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Minus,
  Plus,
  DollarSign,
  Calendar,
  Building,
  FileCheck,
  ChevronDown,
  ChevronRight,
  Copy,
  LayoutGrid
} from 'lucide-react';

interface ComparisonResultsProps {
  comparison: PDFComparison;
  onExport: (format: 'json' | 'csv' | 'txt') => void;
}

export default function ComparisonResults({ comparison, onExport }: ComparisonResultsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'side-by-side' | 'differences' | 'documents'>('overview');
  const [detailedAnalysis, setDetailedAnalysis] = useState<ComparisonAnalysis | null>(null);
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    pricing: true,
    terms: true,
    vendors: false,
    timeline: false
  });

  // Load detailed analysis on mount
  useEffect(() => {
    const loadDetailedAnalysis = async () => {
      try {
        const response = await fetch('/api/detailed-analysis', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ comparison })
        });

        if (response.ok) {
          const analysis = await response.json();
          setDetailedAnalysis(analysis);

          // If this is a quotation comparison and we have side-by-side data, show that tab first
          if (analysis.sideBySide && comparison.comparisonType === 'quotation') {
            setActiveTab('side-by-side');
          }
        }
      } catch (error) {
        console.error('Failed to load detailed analysis:', error);
      }
    };

    loadDetailedAnalysis();
  }, [comparison]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getDifferenceIcon = (type: string) => {
    switch (type) {
      case 'added': return <Plus className="h-4 w-4 text-green-500" />;
      case 'removed': return <Minus className="h-4 w-4 text-red-500" />;
      case 'changed': return <GitCompare className="h-4 w-4 text-blue-500" />;
      default: return <CheckCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSignificanceColor = (significance: string) => {
    switch (significance) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const groupDifferencesByField = () => {
    const groups: Record<string, ComparisonDifference[]> = {};
    comparison.differences.forEach(diff => {
      const category = diff.field.includes('pricing') || diff.field.includes('cost') || diff.field.includes('total') ? 'pricing' :
        diff.field.includes('terms') || diff.field.includes('condition') ? 'terms' :
          diff.field.includes('vendor') || diff.field.includes('supplier') ? 'vendors' :
            diff.field.includes('date') || diff.field.includes('timeline') ? 'timeline' : 'other';

      if (!groups[category]) groups[category] = [];
      groups[category].push(diff);
    });
    return groups;
  };

  const groupedDifferences = groupDifferencesByField();

  const tabs = [
    { id: 'overview', label: 'Overview', icon: GitCompare },
    ...(comparison.comparisonType === 'quotation' ? [
      { id: 'side-by-side', label: 'Side-by-Side', icon: LayoutGrid }
    ] : []),
    { id: 'differences', label: 'Differences', icon: AlertTriangle },
    { id: 'documents', label: 'Documents', icon: FileText }
  ];

  return (
    <div className="w-full max-w-7xl mx-auto bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
              <GitCompare className="h-6 w-6 text-blue-600" />
              <span>PDF Comparison Results</span>
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              Comparing {comparison.documents.length} documents • {new Date(comparison.comparedAt).toLocaleString()}
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => onExport('json')}
              className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Copy className="h-4 w-4" />
              <span>JSON</span>
            </button>
            <button
              onClick={() => onExport('csv')}
              className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Copy className="h-4 w-4" />
              <span>CSV</span>
            </button>
            <button
              onClick={() => onExport('txt')}
              className="flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              <Copy className="h-4 w-4" />
              <span>TXT</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
                {tab.id === 'differences' && (
                  <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                    {comparison.differences.length}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Summary */}
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
              <h3 className="text-lg font-semibold text-blue-900 mb-3">Comparison Summary</h3>
              <p className="text-blue-800">{comparison.summary}</p>
              {comparison.comparisonType === 'quotation' && (
                <div className="mt-4 p-3 bg-white rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-700">
                    💡 <strong>Tip:</strong> Check the "Side-by-Side" tab for detailed quotation comparison with scoring and recommendations.
                  </p>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <StatCard
                icon={FileText}
                label="Documents"
                value={comparison.documents.length.toString()}
                color="blue"
              />
              <StatCard
                icon={AlertTriangle}
                label="Differences"
                value={comparison.differences.length.toString()}
                color="red"
              />
              <StatCard
                icon={TrendingUp}
                label="High Impact"
                value={comparison.differences.filter(d => d.significance === 'high').length.toString()}
                color="orange"
              />
              <StatCard
                icon={CheckCircle}
                label="Type"
                value={comparison.comparisonType}
                color="green"
              />
            </div>

            {/* Recommendations */}
            {comparison.recommendations.length > 0 && (
              <div className="bg-green-50 rounded-lg p-6 border border-green-200">
                <h3 className="text-lg font-semibold text-green-900 mb-3">Recommendations</h3>
                <ul className="space-y-2">
                  {comparison.recommendations.map((rec, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-green-800">{rec}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {activeTab === 'side-by-side' && detailedAnalysis?.sideBySide && (
          <SideBySideComparisonView comparison={detailedAnalysis.sideBySide} />
        )}

        {activeTab === 'side-by-side' && !detailedAnalysis?.sideBySide && (
          <div className="text-center py-12">
            <LayoutGrid className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Side-by-Side Comparison</h3>
            <p className="text-gray-600">Analyzing quotations and generating detailed comparison...</p>
          </div>
        )}

        {activeTab === 'differences' && (
          <div className="space-y-6">
            {Object.entries(groupedDifferences).map(([category, diffs]) => (
              <DifferenceSection
                key={category}
                title={category.charAt(0).toUpperCase() + category.slice(1)}
                differences={diffs}
                isExpanded={expandedSections[category]}
                onToggle={() => toggleSection(category)}
                getDifferenceIcon={getDifferenceIcon}
                getSignificanceColor={getSignificanceColor}
              />
            ))}
          </div>
        )}

        {activeTab === 'documents' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {comparison.documents.map((doc, index) => (
              <DocumentCard key={doc.id} document={doc} index={index} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Helper Components
function StatCard({ icon: Icon, label, value, color }: { icon: any, label: string, value: string, color: string }) {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600 border-blue-200',
    red: 'bg-red-50 text-red-600 border-red-200',
    orange: 'bg-orange-50 text-orange-600 border-orange-200',
    green: 'bg-green-50 text-green-600 border-green-200'
  };

  return (
    <div className={`border rounded-lg p-4 ${colorClasses[color as keyof typeof colorClasses]}`}>
      <div className="flex items-center space-x-2">
        <Icon className="h-5 w-5" />
        <span className="text-sm font-medium">{label}</span>
      </div>
      <p className="text-2xl font-bold mt-2">{value}</p>
    </div>
  );
}

function DifferenceSection({
  title,
  differences,
  isExpanded,
  onToggle,
  getDifferenceIcon,
  getSignificanceColor
}: {
  title: string,
  differences: ComparisonDifference[],
  isExpanded: boolean,
  onToggle: () => void,
  getDifferenceIcon: (type: string) => React.ReactNode,
  getSignificanceColor: (significance: string) => string
}) {
  if (differences.length === 0) return null;

  return (
    <div className="border rounded-lg">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
      >
        <h3 className="text-lg font-semibold text-gray-900">{title} ({differences.length})</h3>
        {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
      </button>
      {isExpanded && (
        <div className="p-4 border-t space-y-3">
          {differences.map((diff, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              {getDifferenceIcon(diff.type)}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900">{diff.field.replace(/_/g, ' ')}</span>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getSignificanceColor(diff.significance)}`}>
                    {diff.significance}
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">{diff.description}</p>
                {(diff.oldValue || diff.newValue) && (
                  <div className="text-xs space-y-1">
                    {diff.oldValue && (
                      <div className="text-red-600">
                        <span className="font-medium">Before:</span> {diff.oldValue}
                      </div>
                    )}
                    {diff.newValue && (
                      <div className="text-green-600">
                        <span className="font-medium">After:</span> {diff.newValue}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function DocumentCard({ document, index }: { document: any, index: number }) {
  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-center space-x-2 mb-3">
        <FileText className="h-5 w-5 text-blue-500" />
        <h3 className="font-semibold text-gray-900">Document {index + 1}</h3>
      </div>
      <div className="space-y-2 text-sm">
        <p><span className="font-medium">Name:</span> {document.fileName}</p>
        <p><span className="font-medium">Type:</span> {document.aiAnalysis?.documentType || 'Unknown'}</p>
        <p><span className="font-medium">Pages:</span> {document.statistics.totalPages}</p>
        <p><span className="font-medium">Words:</span> {document.statistics.totalWords.toLocaleString()}</p>
        {document.aiAnalysis?.quotationDetails?.totals?.total && (
          <p><span className="font-medium">Total:</span> {document.aiAnalysis.quotationDetails.totals.total} {document.aiAnalysis.quotationDetails.totals.currency}</p>
        )}
      </div>
    </div>
  );
}
