import { NextRequest, NextResponse } from 'next/server';
import { PDFComparison } from '@/types/pdf';
import { PDFComparator } from '@/services/pdfComparator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { comparison } = body;

    if (!comparison || !comparison.documents) {
      return NextResponse.json(
        { error: 'Invalid comparison data' },
        { status: 400 }
      );
    }

    const detailedAnalysis = await PDFComparator.getDetailedAnalysis(comparison);
    
    return NextResponse.json(detailedAnalysis);
  } catch (error) {
    console.error('Detailed analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to generate detailed analysis' },
      { status: 500 }
    );
  }
}
