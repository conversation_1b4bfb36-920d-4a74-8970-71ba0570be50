'use client';

import React, { useState } from 'react';
import { SideBySideComparison, QuotationScore } from '@/types/pdf';
import {
  Crown,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Building,
  FileCheck,
  Star,
  AlertTriangle,
  CheckCircle,
  Award,
  Target
} from 'lucide-react';

interface SideBySideComparisonProps {
  comparison: SideBySideComparison;
}

export default function SideBySideComparisonView({ comparison }: SideBySideComparisonProps) {
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'pricing' | 'terms' | 'coverage' | 'exclusions'>('all');

  const categories = [
    { id: 'all', label: 'All Fields', icon: Target },
    { id: 'pricing', label: 'Pricing & Limits', icon: DollarSign },
    { id: 'terms', label: 'Policy Terms', icon: FileCheck },
    { id: 'coverage', label: 'Coverage Extensions', icon: CheckCircle },
    { id: 'exclusions', label: 'Exclusions', icon: AlertTriangle }
  ];

  const filteredMatrix = selectedCategory === 'all'
    ? comparison.comparisonMatrix
    : comparison.comparisonMatrix.filter(item => item.category === selectedCategory);

  const bestDocument = comparison.documents.find(doc => doc.id === comparison.bestChoice.documentId);
  const bestScore = comparison.scores.find(score => score.documentId === comparison.bestChoice.documentId);

  return (
    <div className="space-y-6">
      {/* Best Choice Banner */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="bg-green-600 p-2 rounded-lg">
            <Crown className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-green-900">Recommended Choice</h3>
            <p className="text-green-700">AI Analysis Result</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-green-900 mb-2">{bestDocument?.fileName}</h4>
            <p className="text-green-800 mb-3">{comparison.bestChoice.reason}</p>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-green-700">Confidence:</span>
              <div className="flex-1 bg-green-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{ width: `${comparison.bestChoice.confidence * 100}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-green-900">
                {(comparison.bestChoice.confidence * 100).toFixed(0)}%
              </span>
            </div>
          </div>

          {bestScore && (
            <div>
              <h4 className="font-semibold text-green-900 mb-2">Overall Score</h4>
              <div className="text-3xl font-bold text-green-600 mb-2">
                {bestScore.totalScore}/100
              </div>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${i < Math.floor(bestScore.totalScore / 20) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                  />
                ))}
                <span className="text-sm text-green-700 ml-2">
                  {bestScore.recommendation.charAt(0).toUpperCase() + bestScore.recommendation.slice(1)}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Document Scores Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {comparison.scores.map((score, index) => (
          <ScoreCard
            key={score.documentId}
            score={score}
            rank={index + 1}
            isBest={score.documentId === comparison.bestChoice.documentId}
          />
        ))}
      </div>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => {
          const Icon = category.icon;
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id as any)}
              className={`
                flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors
                ${selectedCategory === category.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }
              `}
            >
              <Icon className="h-4 w-4" />
              <span>{category.label}</span>
            </button>
          );
        })}
      </div>

      {/* Side-by-Side Comparison Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Field
                </th>
                {comparison.documents.map((doc, index) => (
                  <th key={doc.id} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center space-x-2">
                      <span>Document {index + 1}</span>
                      {doc.id === comparison.bestChoice.documentId && (
                        <Crown className="h-4 w-4 text-yellow-500" />
                      )}
                    </div>
                    <div className="text-xs text-gray-400 font-normal mt-1 truncate">
                      {doc.fileName}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMatrix.map((row, rowIndex) => (
                <tr key={rowIndex} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {row.field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <CategoryBadge category={row.category} />
                    </div>
                  </td>
                  {row.values.map((value, valueIndex) => (
                    <td key={valueIndex} className="px-6 py-4 whitespace-nowrap">
                      <div className={`
                        p-3 rounded-lg border-2 transition-all
                        ${value.isBest
                          ? 'bg-green-50 border-green-200 text-green-900'
                          : value.isWorst
                            ? 'bg-red-50 border-red-200 text-red-900'
                            : 'bg-gray-50 border-gray-200 text-gray-900'
                        }
                      `}>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{value.value}</span>
                          <div className="flex items-center space-x-1">
                            {value.isBest && <CheckCircle className="h-4 w-4 text-green-600" />}
                            {value.isWorst && <AlertTriangle className="h-4 w-4 text-red-600" />}
                            <span className="text-xs text-gray-500">
                              {value.score}/100
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Detailed Scores Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {comparison.scores.map((score) => (
          <DetailedScoreCard key={score.documentId} score={score} />
        ))}
      </div>
    </div>
  );
}

// Helper Components
function ScoreCard({ score, rank, isBest }: { score: QuotationScore, rank: number, isBest: boolean }) {
  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 2: return 'bg-gray-100 text-gray-800 border-gray-200';
      case 3: return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className={`
      border-2 rounded-lg p-4 transition-all
      ${isBest ? 'border-green-300 bg-green-50' : 'border-gray-200 bg-white'}
    `}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs font-bold rounded-full border ${getRankColor(rank)}`}>
            #{rank}
          </span>
          {isBest && <Award className="h-5 w-5 text-green-600" />}
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900">{score.totalScore}</div>
          <div className="text-xs text-gray-500">/ 100</div>
        </div>
      </div>

      <h3 className="font-medium text-gray-900 mb-2 truncate">{score.fileName}</h3>

      <div className="space-y-2">
        <ScoreBar label="Pricing" score={score.scores.pricing} />
        <ScoreBar label="Terms" score={score.scores.terms} />
        <ScoreBar label="Vendor" score={score.scores.vendor} />
        <ScoreBar label="Timeline" score={score.scores.timeline} />
      </div>
    </div>
  );
}

function ScoreBar({ label, score }: { label: string, score: number }) {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div>
      <div className="flex justify-between text-xs text-gray-600 mb-1">
        <span>{label}</span>
        <span>{score}/100</span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full ${getScoreColor(score)}`}
          style={{ width: `${score}%` }}
        ></div>
      </div>
    </div>
  );
}

function CategoryBadge({ category }: { category: string }) {
  const colors = {
    pricing: 'bg-green-100 text-green-800',
    terms: 'bg-blue-100 text-blue-800',
    coverage: 'bg-purple-100 text-purple-800',
    exclusions: 'bg-red-100 text-red-800',
    other: 'bg-gray-100 text-gray-800'
  };

  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${colors[category as keyof typeof colors]}`}>
      {category}
    </span>
  );
}

function DetailedScoreCard({ score }: { score: QuotationScore }) {
  return (
    <div className="bg-white border rounded-lg p-4">
      <h3 className="font-semibold text-gray-900 mb-3">{score.fileName}</h3>

      <div className="space-y-3">
        <div>
          <h4 className="text-sm font-medium text-green-700 mb-2">Strengths</h4>
          <ul className="space-y-1">
            {score.strengths.map((strength, index) => (
              <li key={index} className="text-xs text-green-600 flex items-center space-x-1">
                <CheckCircle className="h-3 w-3" />
                <span>{strength}</span>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="text-sm font-medium text-red-700 mb-2">Weaknesses</h4>
          <ul className="space-y-1">
            {score.weaknesses.map((weakness, index) => (
              <li key={index} className="text-xs text-red-600 flex items-center space-x-1">
                <AlertTriangle className="h-3 w-3" />
                <span>{weakness}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
