{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/api/extract-pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { PDFMetadata, PDFStatistics, PDFContent, ExtractedPDFData } from '@/types/pdf';\n\nexport async function POST(request: NextRequest) {\n  try {\n    // Dynamic import to avoid build-time issues\n    const pdfParse = (await import('pdf-parse')).default;\n\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    if (file.type !== 'application/pdf') {\n      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });\n    }\n\n    if (file.size > 10 * 1024 * 1024) { // 10MB limit\n      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 });\n    }\n\n    const startTime = Date.now();\n\n    // Convert file to buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Parse PDF\n    const pdfData = await pdfParse(buffer);\n\n    // Extract metadata\n    const metadata = extractMetadata(pdfData, file);\n\n    // Extract content and structure\n    const content = extractContent(pdfData);\n\n    // Calculate statistics\n    const statistics = calculateStatistics(pdfData, content, file, startTime);\n\n    const result: ExtractedPDFData = {\n      metadata,\n      content,\n      statistics,\n      fileName: file.name,\n      extractedAt: new Date().toISOString()\n    };\n\n    return NextResponse.json(result);\n  } catch (error) {\n    console.error('PDF extraction error:', error);\n    return NextResponse.json(\n      { error: 'Failed to extract PDF data. Please ensure the file is a valid PDF.' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction extractMetadata(pdfData: any, file: File): PDFMetadata {\n  const info = pdfData.info || {};\n\n  return {\n    title: info.Title || 'Unknown',\n    author: info.Author || 'Unknown',\n    subject: info.Subject || 'Not specified',\n    creator: info.Creator || 'Unknown',\n    producer: info.Producer || 'Unknown',\n    creationDate: info.CreationDate ? new Date(info.CreationDate).toLocaleDateString() : 'Unknown',\n    modificationDate: info.ModDate ? new Date(info.ModDate).toLocaleDateString() : 'Unknown',\n    keywords: info.Keywords || 'None',\n    pages: pdfData.numpages || 0\n  };\n}\n\nfunction extractContent(pdfData: any): PDFContent {\n  const fullText = pdfData.text || '';\n  const lines = fullText.split('\\n').filter(line => line.trim());\n\n  // Simple structure extraction\n  const headings = lines.filter(line =>\n    line.length < 100 &&\n    (line.match(/^[A-Z\\s]+$/) || line.match(/^\\d+\\.?\\s+[A-Z]/))\n  );\n\n  const paragraphs = lines.filter(line =>\n    line.length > 50 &&\n    !headings.includes(line)\n  );\n\n  // Extract potential tables (lines with multiple spaces or tabs)\n  const tables = lines.filter(line =>\n    line.includes('\\t') || line.match(/\\s{3,}/)\n  );\n\n  // Extract lists (lines starting with bullets or numbers)\n  const lists = lines.filter(line =>\n    line.match(/^[\\s]*[-•*]\\s/) || line.match(/^[\\s]*\\d+[\\.)]\\s/)\n  );\n\n  // Split text by pages (approximate)\n  const pageTexts = splitTextByPages(fullText, pdfData.numpages);\n\n  return {\n    fullText,\n    pageTexts,\n    structure: {\n      headings: headings.slice(0, 20), // Limit to first 20\n      paragraphs: paragraphs.slice(0, 10), // Limit to first 10\n      tables: tables.slice(0, 10),\n      lists: lists.slice(0, 15)\n    }\n  };\n}\n\nfunction splitTextByPages(text: string, numPages: number): string[] {\n  if (numPages <= 1) return [text];\n\n  const lines = text.split('\\n');\n  const linesPerPage = Math.ceil(lines.length / numPages);\n  const pages: string[] = [];\n\n  for (let i = 0; i < numPages; i++) {\n    const start = i * linesPerPage;\n    const end = Math.min((i + 1) * linesPerPage, lines.length);\n    pages.push(lines.slice(start, end).join('\\n'));\n  }\n\n  return pages;\n}\n\nfunction calculateStatistics(\n  pdfData: any,\n  content: PDFContent,\n  file: File,\n  startTime: number\n): PDFStatistics {\n  const totalCharacters = content.fullText.length;\n  const totalWords = content.fullText.split(/\\s+/).filter(word => word.length > 0).length;\n  const totalLines = content.fullText.split('\\n').length;\n  const totalPages = pdfData.numpages || 1;\n  const processingTime = Date.now() - startTime;\n\n  return {\n    totalPages,\n    totalCharacters,\n    totalWords,\n    totalLines,\n    averageWordsPerPage: Math.round(totalWords / totalPages),\n    fileSize: formatFileSize(file.size),\n    processingTime\n  };\n}\n\nfunction formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,4CAA4C;QAC5C,MAAM,WAAW,CAAC,0IAAyB,EAAE,OAAO;QAEpD,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmC,GAAG;gBAAE,QAAQ;YAAI;QACxF;QAEA,MAAM,YAAY,KAAK,GAAG;QAE1B,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,YAAY;QACZ,MAAM,UAAU,MAAM,SAAS;QAE/B,mBAAmB;QACnB,MAAM,WAAW,gBAAgB,SAAS;QAE1C,gCAAgC;QAChC,MAAM,UAAU,eAAe;QAE/B,uBAAuB;QACvB,MAAM,aAAa,oBAAoB,SAAS,SAAS,MAAM;QAE/D,MAAM,SAA2B;YAC/B;YACA;YACA;YACA,UAAU,KAAK,IAAI;YACnB,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqE,GAC9E;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,gBAAgB,OAAY,EAAE,IAAU;IAC/C,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC;IAE9B,OAAO;QACL,OAAO,KAAK,KAAK,IAAI;QACrB,QAAQ,KAAK,MAAM,IAAI;QACvB,SAAS,KAAK,OAAO,IAAI;QACzB,SAAS,KAAK,OAAO,IAAI;QACzB,UAAU,KAAK,QAAQ,IAAI;QAC3B,cAAc,KAAK,YAAY,GAAG,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,KAAK;QACrF,kBAAkB,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB,KAAK;QAC/E,UAAU,KAAK,QAAQ,IAAI;QAC3B,OAAO,QAAQ,QAAQ,IAAI;IAC7B;AACF;AAEA,SAAS,eAAe,OAAY;IAClC,MAAM,WAAW,QAAQ,IAAI,IAAI;IACjC,MAAM,QAAQ,SAAS,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAE3D,8BAA8B;IAC9B,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAC5B,KAAK,MAAM,GAAG,OACd,CAAC,KAAK,KAAK,CAAC,iBAAiB,KAAK,KAAK,CAAC,kBAAkB;IAG5D,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAC9B,KAAK,MAAM,GAAG,MACd,CAAC,SAAS,QAAQ,CAAC;IAGrB,gEAAgE;IAChE,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAC1B,KAAK,QAAQ,CAAC,SAAS,KAAK,KAAK,CAAC;IAGpC,yDAAyD;IACzD,MAAM,QAAQ,MAAM,MAAM,CAAC,CAAA,OACzB,KAAK,KAAK,CAAC,oBAAoB,KAAK,KAAK,CAAC;IAG5C,oCAAoC;IACpC,MAAM,YAAY,iBAAiB,UAAU,QAAQ,QAAQ;IAE7D,OAAO;QACL;QACA;QACA,WAAW;YACT,UAAU,SAAS,KAAK,CAAC,GAAG;YAC5B,YAAY,WAAW,KAAK,CAAC,GAAG;YAChC,QAAQ,OAAO,KAAK,CAAC,GAAG;YACxB,OAAO,MAAM,KAAK,CAAC,GAAG;QACxB;IACF;AACF;AAEA,SAAS,iBAAiB,IAAY,EAAE,QAAgB;IACtD,IAAI,YAAY,GAAG,OAAO;QAAC;KAAK;IAEhC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,eAAe,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;IAC9C,MAAM,QAAkB,EAAE;IAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;QACjC,MAAM,QAAQ,IAAI;QAClB,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,cAAc,MAAM,MAAM;QACzD,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC;IAC1C;IAEA,OAAO;AACT;AAEA,SAAS,oBACP,OAAY,EACZ,OAAmB,EACnB,IAAU,EACV,SAAiB;IAEjB,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,MAAM;IAC/C,MAAM,aAAa,QAAQ,QAAQ,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IACvF,MAAM,aAAa,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,MAAM;IACtD,MAAM,aAAa,QAAQ,QAAQ,IAAI;IACvC,MAAM,iBAAiB,KAAK,GAAG,KAAK;IAEpC,OAAO;QACL;QACA;QACA;QACA;QACA,qBAAqB,KAAK,KAAK,CAAC,aAAa;QAC7C,UAAU,eAAe,KAAK,IAAI;QAClC;IACF;AACF;AAEA,SAAS,eAAe,KAAa;IACnC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}]}