'use client';

import React, { useState } from 'react';
import { ExtractedPDFData } from '@/types/pdf';
import {
  FileText,
  Info,
  BarChart3,
  Download,
  Copy,
  ChevronDown,
  ChevronRight,
  Clock,
  User,
  Calendar,
  Hash
} from 'lucide-react';

interface PDFResultsProps {
  data: ExtractedPDFData;
  onExport: (format: 'json' | 'csv' | 'txt') => void;
}

export default function PDFResults({ data, onExport }: PDFResultsProps) {
  const [activeTab, setActiveTab] = useState<'metadata' | 'content' | 'statistics'>('metadata');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    headings: true,
    paragraphs: false,
    tables: false,
    lists: false,
    fullText: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const tabs = [
    { id: 'metadata', label: 'Metadata', icon: Info },
    { id: 'content', label: 'Content', icon: FileText },
    { id: 'statistics', label: 'Statistics', icon: BarChart3 }
  ];

  return (
    <div className="w-full max-w-6xl mx-auto bg-white rounded-lg shadow-lg">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">{data.fileName}</h2>
            <p className="text-sm text-gray-500 mt-1">
              Extracted on {new Date(data.extractedAt).toLocaleString()}
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => onExport('json')}
              className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>JSON</span>
            </button>
            <button
              onClick={() => onExport('csv')}
              className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>CSV</span>
            </button>
            <button
              onClick={() => onExport('txt')}
              className="flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>TXT</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'metadata' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <MetadataItem icon={FileText} label="Title" value={data.metadata.title} />
              <MetadataItem icon={User} label="Author" value={data.metadata.author} />
              <MetadataItem icon={Info} label="Subject" value={data.metadata.subject} />
              <MetadataItem icon={FileText} label="Creator" value={data.metadata.creator} />
            </div>
            <div className="space-y-4">
              <MetadataItem icon={FileText} label="Producer" value={data.metadata.producer} />
              <MetadataItem icon={Calendar} label="Created" value={data.metadata.creationDate} />
              <MetadataItem icon={Calendar} label="Modified" value={data.metadata.modificationDate} />
              <MetadataItem icon={Hash} label="Pages" value={data.metadata.pages?.toString()} />
            </div>
            {data.metadata.keywords && data.metadata.keywords !== 'None' && (
              <div className="md:col-span-2">
                <MetadataItem icon={Hash} label="Keywords" value={data.metadata.keywords} />
              </div>
            )}
          </div>
        )}

        {activeTab === 'content' && (
          <div className="space-y-6">
            {/* Structure Sections */}
            {Object.entries(data.content.structure).map(([key, items]) => (
              <StructureSection
                key={key}
                title={key.charAt(0).toUpperCase() + key.slice(1)}
                items={items}
                isExpanded={expandedSections[key]}
                onToggle={() => toggleSection(key)}
                onCopy={() => copyToClipboard(items.join('\n'))}
              />
            ))}

            {/* Full Text */}
            <div className="border rounded-lg">
              <button
                onClick={() => toggleSection('fullText')}
                className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
              >
                <h3 className="text-lg font-semibold text-gray-900">Full Text</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(data.content.fullText);
                    }}
                    className="p-1 hover:bg-gray-200 rounded"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                  {expandedSections.fullText ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
                </div>
              </button>
              {expandedSections.fullText && (
                <div className="p-4 border-t">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 max-h-96 overflow-y-auto">
                    {data.content.fullText}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'statistics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              icon={FileText}
              label="Total Pages"
              value={data.statistics.totalPages.toLocaleString()}
              color="blue"
            />
            <StatCard
              icon={Hash}
              label="Total Words"
              value={data.statistics.totalWords.toLocaleString()}
              color="green"
            />
            <StatCard
              icon={BarChart3}
              label="Total Characters"
              value={data.statistics.totalCharacters.toLocaleString()}
              color="purple"
            />
            <StatCard
              icon={Clock}
              label="Processing Time"
              value={`${data.statistics.processingTime}ms`}
              color="orange"
            />
            <StatCard
              icon={FileText}
              label="File Size"
              value={data.statistics.fileSize}
              color="red"
            />
            <StatCard
              icon={BarChart3}
              label="Avg Words/Page"
              value={data.statistics.averageWordsPerPage.toLocaleString()}
              color="indigo"
            />
            <StatCard
              icon={Hash}
              label="Total Lines"
              value={data.statistics.totalLines.toLocaleString()}
              color="pink"
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Helper Components
function MetadataItem({ icon: Icon, label, value }: { icon: any, label: string, value?: string }) {
  return (
    <div className="flex items-start space-x-3">
      <Icon className="h-5 w-5 text-gray-400 mt-0.5" />
      <div>
        <p className="text-sm font-medium text-gray-900">{label}</p>
        <p className="text-sm text-gray-600">{value || 'Not available'}</p>
      </div>
    </div>
  );
}

function StructureSection({
  title,
  items,
  isExpanded,
  onToggle,
  onCopy
}: {
  title: string,
  items: string[],
  isExpanded: boolean,
  onToggle: () => void,
  onCopy: () => void
}) {
  if (items.length === 0) return null;

  return (
    <div className="border rounded-lg">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
      >
        <h3 className="text-lg font-semibold text-gray-900">{title} ({items.length})</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onCopy();
            }}
            className="p-1 hover:bg-gray-200 rounded"
          >
            <Copy className="h-4 w-4" />
          </button>
          {isExpanded ? <ChevronDown className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
        </div>
      </button>
      {isExpanded && (
        <div className="p-4 border-t">
          <ul className="space-y-2">
            {items.map((item, index) => (
              <li key={index} className="text-sm text-gray-700 p-2 bg-gray-50 rounded">
                {item}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}

function StatCard({
  icon: Icon,
  label,
  value,
  color
}: {
  icon: any,
  label: string,
  value: string,
  color: string
}) {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-600',
    green: 'bg-green-50 text-green-600',
    purple: 'bg-purple-50 text-purple-600',
    orange: 'bg-orange-50 text-orange-600',
    red: 'bg-red-50 text-red-600',
    indigo: 'bg-indigo-50 text-indigo-600',
    pink: 'bg-pink-50 text-pink-600'
  };

  return (
    <div className="bg-white border rounded-lg p-4">
      <div className={`inline-flex p-2 rounded-lg ${colorClasses[color as keyof typeof colorClasses]}`}>
        <Icon className="h-5 w-5" />
      </div>
      <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
      <p className="text-sm text-gray-600">{label}</p>
    </div>
  );
}
