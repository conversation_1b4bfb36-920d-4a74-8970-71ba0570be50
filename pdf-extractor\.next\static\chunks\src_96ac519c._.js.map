{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/PDFUploader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, FileText, AlertCircle } from 'lucide-react';\n\ninterface PDFUploaderProps {\n  onFileUpload: (file: File) => void;\n  isProcessing: boolean;\n}\n\nexport default function PDFUploader({ onFileUpload, isProcessing }: PDFUploaderProps) {\n  const [error, setError] = useState<string | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError(null);\n    \n    if (rejectedFiles.length > 0) {\n      setError('Please upload a valid PDF file.');\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      if (file.size > 10 * 1024 * 1024) { // 10MB limit\n        setError('File size must be less than 10MB.');\n        return;\n      }\n      onFileUpload(file);\n    }\n  }, [onFileUpload]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    multiple: false,\n    disabled: isProcessing\n  });\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto\">\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\n          ${isDragActive \n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400'\n          }\n          ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        <div className=\"flex flex-col items-center space-y-4\">\n          {isProcessing ? (\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          ) : (\n            <Upload className=\"h-12 w-12 text-gray-400\" />\n          )}\n          \n          <div>\n            <p className=\"text-lg font-medium text-gray-700\">\n              {isProcessing \n                ? 'Processing PDF...' \n                : isDragActive \n                  ? 'Drop the PDF file here' \n                  : 'Drag & drop a PDF file here'\n              }\n            </p>\n            {!isProcessing && (\n              <p className=\"text-sm text-gray-500 mt-1\">\n                or click to select a file (max 10MB)\n              </p>\n            )}\n          </div>\n          \n          <FileText className=\"h-8 w-8 text-gray-300\" />\n        </div>\n      </div>\n      \n      {error && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center space-x-2\">\n          <AlertCircle className=\"h-5 w-5 text-red-500\" />\n          <span className=\"text-red-700 text-sm\">{error}</span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;;;AAJA;;;;AAWe,SAAS,YAAY,KAAgD;QAAhD,EAAE,YAAY,EAAE,YAAY,EAAoB,GAAhD;;IAClC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC,eAAuB;YACjD,SAAS;YAET,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,SAAS;gBACT;YACF;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;gBAC7B,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;oBAChC,SAAS;oBACT;gBACF;gBACA,aAAa;YACf;QACF;0CAAG;QAAC;KAAa;IAEjB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;QAC7B;QACA,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,AAAC,uHAMR,OAJA,eACE,+BACA,yCACH,gBACqD,OAApD,eAAe,kCAAkC,IAAG;;kCAGxD,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;4BACZ,6BACC,6LAAC;gCAAI,WAAU;;;;;qDAEf,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAGpB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,eACG,sBACA,eACE,2BACA;;;;;;oCAGP,CAAC,8BACA,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM9C,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAIvB,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAKlD;GAhFwB;;QAqBgC,2KAAA,CAAA,cAAW;;;KArB3C", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/AIAnalysisResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { AIAnalysis, QuotationDetails } from '@/types/pdf';\nimport { \n  Brain, \n  FileText, \n  DollarSign, \n  Calendar, \n  Building, \n  User, \n  Mail, \n  Phone, \n  MapPin,\n  Package,\n  TrendingUp,\n  AlertCircle,\n  CheckCircle,\n  Copy,\n  ChevronDown,\n  ChevronRight\n} from 'lucide-react';\n\ninterface AIAnalysisResultsProps {\n  analysis: AIAnalysis;\n}\n\nexport default function AIAnalysisResults({ analysis }: AIAnalysisResultsProps) {\n  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({\n    quotationDetails: true,\n    insights: true,\n    entities: false,\n    recommendations: false\n  });\n\n  const toggleSection = (section: string) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n  };\n\n  const getDocumentTypeColor = (type: string) => {\n    switch (type) {\n      case 'quotation': return 'bg-blue-100 text-blue-800';\n      case 'invoice': return 'bg-green-100 text-green-800';\n      case 'contract': return 'bg-purple-100 text-purple-800';\n      case 'proposal': return 'bg-orange-100 text-orange-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getConfidenceColor = (confidence: number) => {\n    if (confidence >= 0.8) return 'text-green-600';\n    if (confidence >= 0.6) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border\">\n        <div className=\"flex items-center space-x-3 mb-4\">\n          <div className=\"bg-purple-600 p-2 rounded-lg\">\n            <Brain className=\"h-6 w-6 text-white\" />\n          </div>\n          <div>\n            <h3 className=\"text-xl font-bold text-gray-900\">AI Analysis Results</h3>\n            <p className=\"text-gray-600\">Intelligent document analysis powered by OpenAI</p>\n          </div>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"flex items-center space-x-2\">\n            <FileText className=\"h-5 w-5 text-gray-500\" />\n            <span className=\"text-sm text-gray-600\">Document Type:</span>\n            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDocumentTypeColor(analysis.documentType)}`}>\n              {analysis.documentType.charAt(0).toUpperCase() + analysis.documentType.slice(1)}\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <TrendingUp className=\"h-5 w-5 text-gray-500\" />\n            <span className=\"text-sm text-gray-600\">Confidence:</span>\n            <span className={`font-medium ${getConfidenceColor(analysis.confidence)}`}>\n              {(analysis.confidence * 100).toFixed(1)}%\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Summary */}\n      <div className=\"bg-white rounded-lg border p-6\">\n        <h4 className=\"text-lg font-semibold text-gray-900 mb-3\">Summary</h4>\n        <p className=\"text-gray-700\">{analysis.summary}</p>\n      </div>\n\n      {/* Quotation Details */}\n      {analysis.quotationDetails && (\n        <div className=\"bg-white rounded-lg border\">\n          <button\n            onClick={() => toggleSection('quotationDetails')}\n            className=\"w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors\"\n          >\n            <h4 className=\"text-lg font-semibold text-gray-900\">Quotation Details</h4>\n            {expandedSections.quotationDetails ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n          </button>\n          {expandedSections.quotationDetails && (\n            <div className=\"px-6 pb-6 border-t\">\n              <QuotationDetailsView details={analysis.quotationDetails} />\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Key Insights */}\n      <div className=\"bg-white rounded-lg border\">\n        <button\n          onClick={() => toggleSection('insights')}\n          className=\"w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors\"\n        >\n          <h4 className=\"text-lg font-semibold text-gray-900\">Key Insights</h4>\n          {expandedSections.insights ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n        </button>\n        {expandedSections.insights && (\n          <div className=\"px-6 pb-6 border-t\">\n            <div className=\"space-y-3\">\n              {analysis.keyInsights.map((insight, index) => (\n                <div key={index} className=\"flex items-start space-x-3\">\n                  <CheckCircle className=\"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0\" />\n                  <span className=\"text-gray-700\">{insight}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Recommendations */}\n      {analysis.recommendations && analysis.recommendations.length > 0 && (\n        <div className=\"bg-white rounded-lg border\">\n          <button\n            onClick={() => toggleSection('recommendations')}\n            className=\"w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors\"\n          >\n            <h4 className=\"text-lg font-semibold text-gray-900\">Recommendations</h4>\n            {expandedSections.recommendations ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n          </button>\n          {expandedSections.recommendations && (\n            <div className=\"px-6 pb-6 border-t\">\n              <div className=\"space-y-3\">\n                {analysis.recommendations.map((recommendation, index) => (\n                  <div key={index} className=\"flex items-start space-x-3\">\n                    <AlertCircle className=\"h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0\" />\n                    <span className=\"text-gray-700\">{recommendation}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Extracted Entities */}\n      <div className=\"bg-white rounded-lg border\">\n        <button\n          onClick={() => toggleSection('entities')}\n          className=\"w-full flex items-center justify-between p-6 hover:bg-gray-50 transition-colors\"\n        >\n          <h4 className=\"text-lg font-semibold text-gray-900\">Extracted Entities</h4>\n          {expandedSections.entities ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n        </button>\n        {expandedSections.entities && (\n          <div className=\"px-6 pb-6 border-t\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <EntityList title=\"Dates\" items={analysis.extractedEntities.dates} icon={Calendar} />\n              <EntityList title=\"Amounts\" items={analysis.extractedEntities.amounts} icon={DollarSign} />\n              <EntityList title=\"Companies\" items={analysis.extractedEntities.companies} icon={Building} />\n              <EntityList title=\"Contacts\" items={analysis.extractedEntities.contacts} icon={Mail} />\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// Helper Components\nfunction QuotationDetailsView({ details }: { details: QuotationDetails }) {\n  return (\n    <div className=\"space-y-6 mt-4\">\n      {/* Basic Info */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        {details.quotationNumber && (\n          <InfoItem label=\"Quote Number\" value={details.quotationNumber} icon={FileText} />\n        )}\n        {details.date && (\n          <InfoItem label=\"Date\" value={details.date} icon={Calendar} />\n        )}\n        {details.validUntil && (\n          <InfoItem label=\"Valid Until\" value={details.validUntil} icon={Calendar} />\n        )}\n      </div>\n\n      {/* Vendor & Customer */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {details.vendor && <ContactCard title=\"Vendor\" contact={details.vendor} />}\n        {details.customer && <ContactCard title=\"Customer\" contact={details.customer} />}\n      </div>\n\n      {/* Items */}\n      {details.items && details.items.length > 0 && (\n        <div>\n          <h5 className=\"text-md font-semibold text-gray-900 mb-3\">Line Items</h5>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full border border-gray-200 rounded-lg\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-4 py-2 text-left text-sm font-medium text-gray-700\">Description</th>\n                  <th className=\"px-4 py-2 text-right text-sm font-medium text-gray-700\">Qty</th>\n                  <th className=\"px-4 py-2 text-right text-sm font-medium text-gray-700\">Unit Price</th>\n                  <th className=\"px-4 py-2 text-right text-sm font-medium text-gray-700\">Total</th>\n                </tr>\n              </thead>\n              <tbody>\n                {details.items.map((item, index) => (\n                  <tr key={index} className=\"border-t\">\n                    <td className=\"px-4 py-2 text-sm text-gray-900\">{item.description}</td>\n                    <td className=\"px-4 py-2 text-sm text-gray-900 text-right\">{item.quantity} {item.unit}</td>\n                    <td className=\"px-4 py-2 text-sm text-gray-900 text-right\">{item.unitPrice}</td>\n                    <td className=\"px-4 py-2 text-sm text-gray-900 text-right font-medium\">{item.totalPrice}</td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n\n      {/* Totals */}\n      {details.totals && (\n        <div className=\"bg-gray-50 rounded-lg p-4\">\n          <h5 className=\"text-md font-semibold text-gray-900 mb-3\">Totals</h5>\n          <div className=\"space-y-2\">\n            {details.totals.subtotal && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Subtotal:</span>\n                <span className=\"text-sm font-medium\">{details.totals.subtotal} {details.totals.currency}</span>\n              </div>\n            )}\n            {details.totals.tax && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Tax:</span>\n                <span className=\"text-sm font-medium\">{details.totals.tax} {details.totals.currency}</span>\n              </div>\n            )}\n            {details.totals.discount && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600\">Discount:</span>\n                <span className=\"text-sm font-medium\">-{details.totals.discount} {details.totals.currency}</span>\n              </div>\n            )}\n            {details.totals.total && (\n              <div className=\"flex justify-between border-t pt-2\">\n                <span className=\"text-lg font-semibold text-gray-900\">Total:</span>\n                <span className=\"text-lg font-bold text-green-600\">{details.totals.total} {details.totals.currency}</span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Terms & Notes */}\n      {details.terms && details.terms.length > 0 && (\n        <div>\n          <h5 className=\"text-md font-semibold text-gray-900 mb-3\">Terms & Conditions</h5>\n          <ul className=\"space-y-1\">\n            {details.terms.map((term, index) => (\n              <li key={index} className=\"text-sm text-gray-700\">• {term}</li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      {details.notes && (\n        <div>\n          <h5 className=\"text-md font-semibold text-gray-900 mb-3\">Notes</h5>\n          <p className=\"text-sm text-gray-700\">{details.notes}</p>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction ContactCard({ title, contact }: { title: string, contact: any }) {\n  return (\n    <div className=\"border rounded-lg p-4\">\n      <h5 className=\"text-md font-semibold text-gray-900 mb-3\">{title}</h5>\n      <div className=\"space-y-2\">\n        {contact.name && (\n          <div className=\"flex items-center space-x-2\">\n            <User className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm text-gray-700\">{contact.name}</span>\n          </div>\n        )}\n        {contact.email && (\n          <div className=\"flex items-center space-x-2\">\n            <Mail className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm text-gray-700\">{contact.email}</span>\n          </div>\n        )}\n        {contact.phone && (\n          <div className=\"flex items-center space-x-2\">\n            <Phone className=\"h-4 w-4 text-gray-500\" />\n            <span className=\"text-sm text-gray-700\">{contact.phone}</span>\n          </div>\n        )}\n        {contact.address && (\n          <div className=\"flex items-start space-x-2\">\n            <MapPin className=\"h-4 w-4 text-gray-500 mt-0.5\" />\n            <span className=\"text-sm text-gray-700\">{contact.address}</span>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\nfunction InfoItem({ label, value, icon: Icon }: { label: string, value: string, icon: any }) {\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <Icon className=\"h-4 w-4 text-gray-500\" />\n      <div>\n        <p className=\"text-xs text-gray-500\">{label}</p>\n        <p className=\"text-sm font-medium text-gray-900\">{value}</p>\n      </div>\n    </div>\n  );\n}\n\nfunction EntityList({ title, items, icon: Icon }: { title: string, items: string[], icon: any }) {\n  if (items.length === 0) return null;\n\n  return (\n    <div>\n      <div className=\"flex items-center space-x-2 mb-2\">\n        <Icon className=\"h-4 w-4 text-gray-500\" />\n        <h6 className=\"text-sm font-medium text-gray-900\">{title}</h6>\n      </div>\n      <div className=\"space-y-1\">\n        {items.slice(0, 5).map((item, index) => (\n          <div key={index} className=\"text-sm text-gray-700 bg-gray-50 px-2 py-1 rounded\">\n            {item}\n          </div>\n        ))}\n        {items.length > 5 && (\n          <div className=\"text-xs text-gray-500\">\n            +{items.length - 5} more items\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;AA2Be,SAAS,kBAAkB,KAAoC;QAApC,EAAE,QAAQ,EAA0B,GAApC;;IACxC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAChF,kBAAkB;QAClB,UAAU;QACV,UAAU;QACV,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,cAAc,KAAK,OAAO;QAC9B,IAAI,cAAc,KAAK,OAAO;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAIjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAW,AAAC,8CAAyF,OAA5C,qBAAqB,SAAS,YAAY;kDACtG,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,YAAY,CAAC,KAAK,CAAC;;;;;;;;;;;;0CAGjF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAW,AAAC,eAAsD,OAAxC,mBAAmB,SAAS,UAAU;;4CACnE,CAAC,SAAS,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAiB,SAAS,OAAO;;;;;;;;;;;;YAI/C,SAAS,gBAAgB,kBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,iBAAiB,gBAAgB,iBAAG,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAAe,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;oBAEpG,iBAAiB,gBAAgB,kBAChC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAqB,SAAS,SAAS,gBAAgB;;;;;;;;;;;;;;;;;0BAOhE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,iBAAiB,QAAQ,iBAAG,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAAe,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;oBAE5F,iBAAiB,QAAQ,kBACxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,sBAClC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;;mCAFzB;;;;;;;;;;;;;;;;;;;;;YAWnB,SAAS,eAAe,IAAI,SAAS,eAAe,CAAC,MAAM,GAAG,mBAC7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,iBAAiB,eAAe,iBAAG,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAAe,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;oBAEnG,iBAAiB,eAAe,kBAC/B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,eAAe,CAAC,GAAG,CAAC,CAAC,gBAAgB,sBAC7C,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;;mCAFzB;;;;;;;;;;;;;;;;;;;;;0BAYtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,cAAc;wBAC7B,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;4BACnD,iBAAiB,QAAQ,iBAAG,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAAe,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;oBAE5F,iBAAiB,QAAQ,kBACxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAW,OAAM;oCAAQ,OAAO,SAAS,iBAAiB,CAAC,KAAK;oCAAE,MAAM,6MAAA,CAAA,WAAQ;;;;;;8CACjF,6LAAC;oCAAW,OAAM;oCAAU,OAAO,SAAS,iBAAiB,CAAC,OAAO;oCAAE,MAAM,qNAAA,CAAA,aAAU;;;;;;8CACvF,6LAAC;oCAAW,OAAM;oCAAY,OAAO,SAAS,iBAAiB,CAAC,SAAS;oCAAE,MAAM,6MAAA,CAAA,WAAQ;;;;;;8CACzF,6LAAC;oCAAW,OAAM;oCAAW,OAAO,SAAS,iBAAiB,CAAC,QAAQ;oCAAE,MAAM,qMAAA,CAAA,OAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjG;GAjKwB;KAAA;AAmKxB,oBAAoB;AACpB,SAAS,qBAAqB,KAA0C;QAA1C,EAAE,OAAO,EAAiC,GAA1C;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,eAAe,kBACtB,6LAAC;wBAAS,OAAM;wBAAe,OAAO,QAAQ,eAAe;wBAAE,MAAM,iNAAA,CAAA,WAAQ;;;;;;oBAE9E,QAAQ,IAAI,kBACX,6LAAC;wBAAS,OAAM;wBAAO,OAAO,QAAQ,IAAI;wBAAE,MAAM,6MAAA,CAAA,WAAQ;;;;;;oBAE3D,QAAQ,UAAU,kBACjB,6LAAC;wBAAS,OAAM;wBAAc,OAAO,QAAQ,UAAU;wBAAE,MAAM,6MAAA,CAAA,WAAQ;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,MAAM,kBAAI,6LAAC;wBAAY,OAAM;wBAAS,SAAS,QAAQ,MAAM;;;;;;oBACrE,QAAQ,QAAQ,kBAAI,6LAAC;wBAAY,OAAM;wBAAW,SAAS,QAAQ,QAAQ;;;;;;;;;;;;YAI7E,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACvC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAwD;;;;;;0DACtE,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;0DACvE,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;0DACvE,6LAAC;gDAAG,WAAU;0DAAyD;;;;;;;;;;;;;;;;;8CAG3E,6LAAC;8CACE,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;4CAAe,WAAU;;8DACxB,6LAAC;oDAAG,WAAU;8DAAmC,KAAK,WAAW;;;;;;8DACjE,6LAAC;oDAAG,WAAU;;wDAA8C,KAAK,QAAQ;wDAAC;wDAAE,KAAK,IAAI;;;;;;;8DACrF,6LAAC;oDAAG,WAAU;8DAA8C,KAAK,SAAS;;;;;;8DAC1E,6LAAC;oDAAG,WAAU;8DAA0D,KAAK,UAAU;;;;;;;2CAJhF;;;;;;;;;;;;;;;;;;;;;;;;;;;YAcpB,QAAQ,MAAM,kBACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,MAAM,CAAC,QAAQ,kBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAuB,QAAQ,MAAM,CAAC,QAAQ;4CAAC;4CAAE,QAAQ,MAAM,CAAC,QAAQ;;;;;;;;;;;;;4BAG3F,QAAQ,MAAM,CAAC,GAAG,kBACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAuB,QAAQ,MAAM,CAAC,GAAG;4CAAC;4CAAE,QAAQ,MAAM,CAAC,QAAQ;;;;;;;;;;;;;4BAGtF,QAAQ,MAAM,CAAC,QAAQ,kBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;;4CAAsB;4CAAE,QAAQ,MAAM,CAAC,QAAQ;4CAAC;4CAAE,QAAQ,MAAM,CAAC,QAAQ;;;;;;;;;;;;;4BAG5F,QAAQ,MAAM,CAAC,KAAK,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAsC;;;;;;kDACtD,6LAAC;wCAAK,WAAU;;4CAAoC,QAAQ,MAAM,CAAC,KAAK;4CAAC;4CAAE,QAAQ,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;YAQ3G,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACvC,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAG,WAAU;kCACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gCAAe,WAAU;;oCAAwB;oCAAG;;+BAA5C;;;;;;;;;;;;;;;;YAMhB,QAAQ,KAAK,kBACZ,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAE,WAAU;kCAAyB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;AAK7D;MAxGS;AA0GT,SAAS,YAAY,KAAmD;QAAnD,EAAE,KAAK,EAAE,OAAO,EAAmC,GAAnD;IACnB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,IAAI,kBACX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAyB,QAAQ,IAAI;;;;;;;;;;;;oBAGxD,QAAQ,KAAK,kBACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAyB,QAAQ,KAAK;;;;;;;;;;;;oBAGzD,QAAQ,KAAK,kBACZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAyB,QAAQ,KAAK;;;;;;;;;;;;oBAGzD,QAAQ,OAAO,kBACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CAAyB,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMpE;MAhCS;AAkCT,SAAS,SAAS,KAAyE;QAAzE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,EAA+C,GAAzE;IAChB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;;;;;;0BAChB,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAyB;;;;;;kCACtC,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;;AAI1D;MAVS;AAYT,SAAS,WAAW,KAA2E;QAA3E,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,IAAI,EAAiD,GAA3E;IAClB,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;;;;;;kCAChB,6LAAC;wBAAG,WAAU;kCAAqC;;;;;;;;;;;;0BAErD,6LAAC;gBAAI,WAAU;;oBACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC5B,6LAAC;4BAAgB,WAAU;sCACxB;2BADO;;;;;oBAIX,MAAM,MAAM,GAAG,mBACd,6LAAC;wBAAI,WAAU;;4BAAwB;4BACnC,MAAM,MAAM,GAAG;4BAAE;;;;;;;;;;;;;;;;;;;AAM/B;MAvBS", "debugId": null}}, {"offset": {"line": 1366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/PDFResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { ExtractedPDFData } from '@/types/pdf';\nimport AIAnalysisResults from './AIAnalysisResults';\nimport {\n  FileText,\n  Info,\n  BarChart3,\n  Download,\n  Copy,\n  ChevronDown,\n  Brain,\n  ChevronRight,\n  Clock,\n  User,\n  Calendar,\n  Hash\n} from 'lucide-react';\n\ninterface PDFResultsProps {\n  data: ExtractedPDFData;\n  onExport: (format: 'json' | 'csv' | 'txt') => void;\n}\n\nexport default function PDFResults({ data, onExport }: PDFResultsProps) {\n  const [activeTab, setActiveTab] = useState<'metadata' | 'content' | 'statistics' | 'ai-analysis'>(\n    data.aiAnalysis ? 'ai-analysis' : 'metadata'\n  );\n  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({\n    headings: true,\n    paragraphs: false,\n    tables: false,\n    lists: false,\n    fullText: false\n  });\n\n  const toggleSection = (section: string) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n  };\n\n  const tabs = [\n    ...(data.aiAnalysis ? [{ id: 'ai-analysis', label: 'AI Analysis', icon: Brain }] : []),\n    { id: 'metadata', label: 'Metadata', icon: Info },\n    { id: 'content', label: 'Content', icon: FileText },\n    { id: 'statistics', label: 'Statistics', icon: BarChart3 }\n  ];\n\n  return (\n    <div className=\"w-full max-w-6xl mx-auto bg-white rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-6\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">{data.fileName}</h2>\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Extracted on {new Date(data.extractedAt).toLocaleString()}\n            </p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => onExport('json')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>JSON</span>\n            </button>\n            <button\n              onClick={() => onExport('csv')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>CSV</span>\n            </button>\n            <button\n              onClick={() => onExport('txt')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>TXT</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`\n                  flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors\n                  ${activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                  }\n                `}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        {activeTab === 'ai-analysis' && data.aiAnalysis && (\n          <AIAnalysisResults analysis={data.aiAnalysis} />\n        )}\n\n        {activeTab === 'metadata' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <MetadataItem icon={FileText} label=\"Title\" value={data.metadata.title} />\n              <MetadataItem icon={User} label=\"Author\" value={data.metadata.author} />\n              <MetadataItem icon={Info} label=\"Subject\" value={data.metadata.subject} />\n              <MetadataItem icon={FileText} label=\"Creator\" value={data.metadata.creator} />\n            </div>\n            <div className=\"space-y-4\">\n              <MetadataItem icon={FileText} label=\"Producer\" value={data.metadata.producer} />\n              <MetadataItem icon={Calendar} label=\"Created\" value={data.metadata.creationDate} />\n              <MetadataItem icon={Calendar} label=\"Modified\" value={data.metadata.modificationDate} />\n              <MetadataItem icon={Hash} label=\"Pages\" value={data.metadata.pages?.toString()} />\n            </div>\n            {data.metadata.keywords && data.metadata.keywords !== 'None' && (\n              <div className=\"md:col-span-2\">\n                <MetadataItem icon={Hash} label=\"Keywords\" value={data.metadata.keywords} />\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'content' && (\n          <div className=\"space-y-6\">\n            {/* Structure Sections */}\n            {Object.entries(data.content.structure).map(([key, items]) => (\n              <StructureSection\n                key={key}\n                title={key.charAt(0).toUpperCase() + key.slice(1)}\n                items={items}\n                isExpanded={expandedSections[key]}\n                onToggle={() => toggleSection(key)}\n                onCopy={() => copyToClipboard(items.join('\\n'))}\n              />\n            ))}\n\n            {/* Full Text */}\n            <div className=\"border rounded-lg\">\n              <button\n                onClick={() => toggleSection('fullText')}\n                className=\"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\"\n              >\n                <h3 className=\"text-lg font-semibold text-gray-900\">Full Text</h3>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      copyToClipboard(data.content.fullText);\n                    }}\n                    className=\"p-1 hover:bg-gray-200 rounded\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </button>\n                  {expandedSections.fullText ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n                </div>\n              </button>\n              {expandedSections.fullText && (\n                <div className=\"p-4 border-t\">\n                  <pre className=\"whitespace-pre-wrap text-sm text-gray-700 max-h-96 overflow-y-auto\">\n                    {data.content.fullText}\n                  </pre>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'statistics' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <StatCard\n              icon={FileText}\n              label=\"Total Pages\"\n              value={data.statistics.totalPages.toLocaleString()}\n              color=\"blue\"\n            />\n            <StatCard\n              icon={Hash}\n              label=\"Total Words\"\n              value={data.statistics.totalWords.toLocaleString()}\n              color=\"green\"\n            />\n            <StatCard\n              icon={BarChart3}\n              label=\"Total Characters\"\n              value={data.statistics.totalCharacters.toLocaleString()}\n              color=\"purple\"\n            />\n            <StatCard\n              icon={Clock}\n              label=\"Processing Time\"\n              value={`${data.statistics.processingTime}ms`}\n              color=\"orange\"\n            />\n            <StatCard\n              icon={FileText}\n              label=\"File Size\"\n              value={data.statistics.fileSize}\n              color=\"red\"\n            />\n            <StatCard\n              icon={BarChart3}\n              label=\"Avg Words/Page\"\n              value={data.statistics.averageWordsPerPage.toLocaleString()}\n              color=\"indigo\"\n            />\n            <StatCard\n              icon={Hash}\n              label=\"Total Lines\"\n              value={data.statistics.totalLines.toLocaleString()}\n              color=\"pink\"\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// Helper Components\nfunction MetadataItem({ icon: Icon, label, value }: { icon: any, label: string, value?: string }) {\n  return (\n    <div className=\"flex items-start space-x-3\">\n      <Icon className=\"h-5 w-5 text-gray-400 mt-0.5\" />\n      <div>\n        <p className=\"text-sm font-medium text-gray-900\">{label}</p>\n        <p className=\"text-sm text-gray-600\">{value || 'Not available'}</p>\n      </div>\n    </div>\n  );\n}\n\nfunction StructureSection({\n  title,\n  items,\n  isExpanded,\n  onToggle,\n  onCopy\n}: {\n  title: string,\n  items: string[],\n  isExpanded: boolean,\n  onToggle: () => void,\n  onCopy: () => void\n}) {\n  if (items.length === 0) return null;\n\n  return (\n    <div className=\"border rounded-lg\">\n      <button\n        onClick={onToggle}\n        className=\"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\"\n      >\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title} ({items.length})</h3>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={(e) => {\n              e.stopPropagation();\n              onCopy();\n            }}\n            className=\"p-1 hover:bg-gray-200 rounded\"\n          >\n            <Copy className=\"h-4 w-4\" />\n          </button>\n          {isExpanded ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n        </div>\n      </button>\n      {isExpanded && (\n        <div className=\"p-4 border-t\">\n          <ul className=\"space-y-2\">\n            {items.map((item, index) => (\n              <li key={index} className=\"text-sm text-gray-700 p-2 bg-gray-50 rounded\">\n                {item}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction StatCard({\n  icon: Icon,\n  label,\n  value,\n  color\n}: {\n  icon: any,\n  label: string,\n  value: string,\n  color: string\n}) {\n  const colorClasses = {\n    blue: 'bg-blue-50 text-blue-600',\n    green: 'bg-green-50 text-green-600',\n    purple: 'bg-purple-50 text-purple-600',\n    orange: 'bg-orange-50 text-orange-600',\n    red: 'bg-red-50 text-red-600',\n    indigo: 'bg-indigo-50 text-indigo-600',\n    pink: 'bg-pink-50 text-pink-600'\n  };\n\n  return (\n    <div className=\"bg-white border rounded-lg p-4\">\n      <div className={`inline-flex p-2 rounded-lg ${colorClasses[color as keyof typeof colorClasses]}`}>\n        <Icon className=\"h-5 w-5\" />\n      </div>\n      <p className=\"text-2xl font-bold text-gray-900 mt-2\">{value}</p>\n      <p className=\"text-sm text-gray-600\">{label}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;AAyBe,SAAS,WAAW,KAAmC;QAAnC,EAAE,IAAI,EAAE,QAAQ,EAAmB,GAAnC;QA8G0B;;IA7G3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,KAAK,UAAU,GAAG,gBAAgB;IAEpC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAChF,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,MAAM,OAAO;WACP,KAAK,UAAU,GAAG;YAAC;gBAAE,IAAI;gBAAe,OAAO;gBAAe,MAAM,uMAAA,CAAA,QAAK;YAAC;SAAE,GAAG,EAAE;QACrF;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,qMAAA,CAAA,OAAI;QAAC;QAChD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,iNAAA,CAAA,WAAQ;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,qNAAA,CAAA,YAAS;QAAC;KAC1D;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC,KAAK,QAAQ;;;;;;8CAC/D,6LAAC;oCAAE,WAAU;;wCAA6B;wCAC1B,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc;;;;;;;;;;;;;sCAG3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,qBACE,6LAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,AAAC,4HAKT,OAHC,cAAc,IAAI,EAAE,GAClB,kCACA,wDACH;;8CAGH,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;8CAAM,IAAI,KAAK;;;;;;;2BAXX,IAAI,EAAE;;;;;oBAcjB;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,iBAAiB,KAAK,UAAU,kBAC7C,6LAAC,0IAAA,CAAA,UAAiB;wBAAC,UAAU,KAAK,UAAU;;;;;;oBAG7C,cAAc,4BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAa,MAAM,iNAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAQ,OAAO,KAAK,QAAQ,CAAC,KAAK;;;;;;kDACtE,6LAAC;wCAAa,MAAM,qMAAA,CAAA,OAAI;wCAAE,OAAM;wCAAS,OAAO,KAAK,QAAQ,CAAC,MAAM;;;;;;kDACpE,6LAAC;wCAAa,MAAM,qMAAA,CAAA,OAAI;wCAAE,OAAM;wCAAU,OAAO,KAAK,QAAQ,CAAC,OAAO;;;;;;kDACtE,6LAAC;wCAAa,MAAM,iNAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAU,OAAO,KAAK,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAE5E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAa,MAAM,iNAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAW,OAAO,KAAK,QAAQ,CAAC,QAAQ;;;;;;kDAC5E,6LAAC;wCAAa,MAAM,6MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAU,OAAO,KAAK,QAAQ,CAAC,YAAY;;;;;;kDAC/E,6LAAC;wCAAa,MAAM,6MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAW,OAAO,KAAK,QAAQ,CAAC,gBAAgB;;;;;;kDACpF,6LAAC;wCAAa,MAAM,qMAAA,CAAA,OAAI;wCAAE,OAAM;wCAAQ,KAAK,GAAE,uBAAA,KAAK,QAAQ,CAAC,KAAK,cAAnB,2CAAA,qBAAqB,QAAQ;;;;;;;;;;;;4BAE7E,KAAK,QAAQ,CAAC,QAAQ,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,wBACpD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAa,MAAM,qMAAA,CAAA,OAAI;oCAAE,OAAM;oCAAW,OAAO,KAAK,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;;;;;oBAM/E,cAAc,2BACb,6LAAC;wBAAI,WAAU;;4BAEZ,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;oCAAC,CAAC,KAAK,MAAM;qDACvD,6LAAC;oCAEC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;oCAC/C,OAAO;oCACP,YAAY,gBAAgB,CAAC,IAAI;oCACjC,UAAU,IAAM,cAAc;oCAC9B,QAAQ,IAAM,gBAAgB,MAAM,IAAI,CAAC;mCALpC;;;;;;0CAUT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,6LAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,gBAAgB,KAAK,OAAO,CAAC,QAAQ;wDACvC;wDACA,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;oDAEjB,iBAAiB,QAAQ,iBAAG,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAAe,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;oCAG9F,iBAAiB,QAAQ,kBACxB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;oBAQjC,cAAc,8BACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAM,iNAAA,CAAA,WAAQ;gCACd,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,cAAc;gCAChD,OAAM;;;;;;0CAER,6LAAC;gCACC,MAAM,qMAAA,CAAA,OAAI;gCACV,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,cAAc;gCAChD,OAAM;;;;;;0CAER,6LAAC;gCACC,MAAM,qNAAA,CAAA,YAAS;gCACf,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,eAAe,CAAC,cAAc;gCACrD,OAAM;;;;;;0CAER,6LAAC;gCACC,MAAM,uMAAA,CAAA,QAAK;gCACX,OAAM;gCACN,OAAO,AAAC,GAAiC,OAA/B,KAAK,UAAU,CAAC,cAAc,EAAC;gCACzC,OAAM;;;;;;0CAER,6LAAC;gCACC,MAAM,iNAAA,CAAA,WAAQ;gCACd,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,QAAQ;gCAC/B,OAAM;;;;;;0CAER,6LAAC;gCACC,MAAM,qNAAA,CAAA,YAAS;gCACf,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,mBAAmB,CAAC,cAAc;gCACzD,OAAM;;;;;;0CAER,6LAAC;gCACC,MAAM,qMAAA,CAAA,OAAI;gCACV,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,cAAc;gCAChD,OAAM;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GAtNwB;KAAA;AAwNxB,oBAAoB;AACpB,SAAS,aAAa,KAA0E;QAA1E,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,KAAK,EAAgD,GAA1E;IACpB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,WAAU;;;;;;0BAChB,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAClD,6LAAC;wBAAE,WAAU;kCAAyB,SAAS;;;;;;;;;;;;;;;;;;AAIvD;MAVS;AAYT,SAAS,iBAAiB,KAYzB;QAZyB,EACxB,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,MAAM,EAOP,GAZyB;IAaxB,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC;wBAAG,WAAU;;4BAAuC;4BAAM;4BAAG,MAAM,MAAM;4BAAC;;;;;;;kCAC3E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;4BAEjB,2BAAa,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAAe,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAG/E,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4BAAe,WAAU;sCACvB;2BADM;;;;;;;;;;;;;;;;;;;;;AASvB;MAhDS;AAkDT,SAAS,SAAS,KAUjB;QAViB,EAChB,MAAM,IAAI,EACV,KAAK,EACL,KAAK,EACL,KAAK,EAMN,GAViB;IAWhB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAW,AAAC,8BAA8E,OAAjD,YAAY,CAAC,MAAmC;0BAC5F,cAAA,6LAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAE,WAAU;0BAAyC;;;;;;0BACtD,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C;MA9BS", "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/pdfExtractor.ts"], "sourcesContent": ["import { ExtractedPDFData } from '@/types/pdf';\n\nexport class PDFExtractorService {\n  static async extractPDFData(file: File): Promise<ExtractedPDFData> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await fetch('/api/extract-pdf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to extract PDF data');\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error('PDF extraction error:', error);\n      throw new Error(error instanceof Error ? error.message : 'Failed to extract PDF data. Please ensure the file is a valid PDF.');\n    }\n  }\n\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,aAAa,eAAe,IAAU,EAA6B;QACjE,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3D;IACF;AAEF", "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/exportService.ts"], "sourcesContent": ["import { ExtractedPDFData } from '@/types/pdf';\n\nexport class ExportService {\n  static exportAsJSON(data: ExtractedPDFData): void {\n    const jsonString = JSON.stringify(data, null, 2);\n    this.downloadFile(jsonString, `${data.fileName}_extracted.json`, 'application/json');\n  }\n\n  static exportAsCSV(data: ExtractedPDFData): void {\n    const csvContent = this.convertToCSV(data);\n    this.downloadFile(csvContent, `${data.fileName}_extracted.csv`, 'text/csv');\n  }\n\n  static exportAsText(data: ExtractedPDFData): void {\n    const textContent = this.convertToText(data);\n    this.downloadFile(textContent, `${data.fileName}_extracted.txt`, 'text/plain');\n  }\n\n  private static convertToCSV(data: ExtractedPDFData): string {\n    const rows: string[] = [];\n\n    // Header\n    rows.push('Category,Field,Value');\n\n    // Metadata\n    Object.entries(data.metadata).forEach(([key, value]) => {\n      rows.push(`Metadata,${key},\"${String(value).replace(/\"/g, '\"\"')}\"`);\n    });\n\n    // Statistics\n    Object.entries(data.statistics).forEach(([key, value]) => {\n      rows.push(`Statistics,${key},\"${String(value).replace(/\"/g, '\"\"')}\"`);\n    });\n\n    // Content structure counts\n    Object.entries(data.content.structure).forEach(([key, items]) => {\n      rows.push(`Content Structure,${key} Count,${items.length}`);\n    });\n\n    // AI Analysis data\n    if (data.aiAnalysis) {\n      rows.push(`AI Analysis,Document Type,\"${data.aiAnalysis.documentType}\"`);\n      rows.push(`AI Analysis,Confidence,${data.aiAnalysis.confidence}`);\n      rows.push(`AI Analysis,Summary,\"${data.aiAnalysis.summary.replace(/\"/g, '\"\"')}\"`);\n\n      if (data.aiAnalysis.quotationDetails?.quotationNumber) {\n        rows.push(`AI Analysis,Quote Number,\"${data.aiAnalysis.quotationDetails.quotationNumber}\"`);\n      }\n      if (data.aiAnalysis.quotationDetails?.totals?.total) {\n        rows.push(`AI Analysis,Total Amount,${data.aiAnalysis.quotationDetails.totals.total}`);\n      }\n    }\n\n    // Add some sample content\n    rows.push(`Content,Full Text Length,${data.content.fullText.length}`);\n    rows.push(`Content,Sample Text,\"${data.content.fullText.substring(0, 200).replace(/\"/g, '\"\"')}...\"`);\n\n    return rows.join('\\n');\n  }\n\n  private static convertToText(data: ExtractedPDFData): string {\n    const sections: string[] = [];\n\n    // Header\n    sections.push('PDF EXTRACTION REPORT');\n    sections.push('='.repeat(50));\n    sections.push(`File: ${data.fileName}`);\n    sections.push(`Extracted: ${new Date(data.extractedAt).toLocaleString()}`);\n    sections.push('');\n\n    // Metadata\n    sections.push('METADATA');\n    sections.push('-'.repeat(20));\n    Object.entries(data.metadata).forEach(([key, value]) => {\n      sections.push(`${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`);\n    });\n    sections.push('');\n\n    // Statistics\n    sections.push('STATISTICS');\n    sections.push('-'.repeat(20));\n    Object.entries(data.statistics).forEach(([key, value]) => {\n      const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());\n      sections.push(`${label}: ${value}`);\n    });\n    sections.push('');\n\n    // AI Analysis\n    if (data.aiAnalysis) {\n      sections.push('AI ANALYSIS');\n      sections.push('-'.repeat(20));\n      sections.push(`Document Type: ${data.aiAnalysis.documentType}`);\n      sections.push(`Confidence: ${(data.aiAnalysis.confidence * 100).toFixed(1)}%`);\n      sections.push(`Summary: ${data.aiAnalysis.summary}`);\n      sections.push('');\n\n      if (data.aiAnalysis.quotationDetails) {\n        sections.push('QUOTATION DETAILS');\n        sections.push('-'.repeat(20));\n        const qd = data.aiAnalysis.quotationDetails;\n        if (qd.quotationNumber) sections.push(`Quote Number: ${qd.quotationNumber}`);\n        if (qd.date) sections.push(`Date: ${qd.date}`);\n        if (qd.validUntil) sections.push(`Valid Until: ${qd.validUntil}`);\n        if (qd.vendor?.name) sections.push(`Vendor: ${qd.vendor.name}`);\n        if (qd.customer?.name) sections.push(`Customer: ${qd.customer.name}`);\n        if (qd.totals?.total) sections.push(`Total: ${qd.totals.total} ${qd.totals.currency || ''}`);\n        sections.push('');\n      }\n\n      if (data.aiAnalysis.keyInsights.length > 0) {\n        sections.push('KEY INSIGHTS');\n        sections.push('-'.repeat(20));\n        data.aiAnalysis.keyInsights.forEach((insight, index) => {\n          sections.push(`${index + 1}. ${insight}`);\n        });\n        sections.push('');\n      }\n    }\n\n    // Content Structure\n    sections.push('CONTENT STRUCTURE');\n    sections.push('-'.repeat(20));\n    Object.entries(data.content.structure).forEach(([key, items]) => {\n      if (items.length > 0) {\n        sections.push(`${key.toUpperCase()} (${items.length} items):`);\n        items.slice(0, 5).forEach((item, index) => {\n          sections.push(`  ${index + 1}. ${item.substring(0, 100)}${item.length > 100 ? '...' : ''}`);\n        });\n        if (items.length > 5) {\n          sections.push(`  ... and ${items.length - 5} more items`);\n        }\n        sections.push('');\n      }\n    });\n\n    // Full Text (truncated)\n    sections.push('FULL TEXT (First 1000 characters)');\n    sections.push('-'.repeat(40));\n    sections.push(data.content.fullText.substring(0, 1000));\n    if (data.content.fullText.length > 1000) {\n      sections.push('\\n... (text truncated)');\n    }\n\n    return sections.join('\\n');\n  }\n\n  private static downloadFile(content: string, filename: string, mimeType: string): void {\n    const blob = new Blob([content], { type: mimeType });\n    const url = URL.createObjectURL(blob);\n\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n\n    // Clean up the URL object\n    URL.revokeObjectURL(url);\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,aAAa,IAAsB,EAAQ;QAChD,MAAM,aAAa,KAAK,SAAS,CAAC,MAAM,MAAM;QAC9C,IAAI,CAAC,YAAY,CAAC,YAAY,AAAC,GAAgB,OAAd,KAAK,QAAQ,EAAC,oBAAkB;IACnE;IAEA,OAAO,YAAY,IAAsB,EAAQ;QAC/C,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,YAAY,AAAC,GAAgB,OAAd,KAAK,QAAQ,EAAC,mBAAiB;IAClE;IAEA,OAAO,aAAa,IAAsB,EAAQ;QAChD,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,aAAa,AAAC,GAAgB,OAAd,KAAK,QAAQ,EAAC,mBAAiB;IACnE;IAEA,OAAe,aAAa,IAAsB,EAAU;QAC1D,MAAM,OAAiB,EAAE;QAEzB,SAAS;QACT,KAAK,IAAI,CAAC;QAEV,WAAW;QACX,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YACjD,KAAK,IAAI,CAAC,AAAC,YAAmB,OAAR,KAAI,MAAsC,OAAlC,OAAO,OAAO,OAAO,CAAC,MAAM,OAAM;QAClE;QAEA,aAAa;QACb,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YACnD,KAAK,IAAI,CAAC,AAAC,cAAqB,OAAR,KAAI,MAAsC,OAAlC,OAAO,OAAO,OAAO,CAAC,MAAM,OAAM;QACpE;QAEA,2BAA2B;QAC3B,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YAC1D,KAAK,IAAI,CAAC,AAAC,qBAAiC,OAAb,KAAI,WAAsB,OAAb,MAAM,MAAM;QAC1D;QAEA,mBAAmB;QACnB,IAAI,KAAK,UAAU,EAAE;gBAKf,mCAGA,0CAAA;YAPJ,KAAK,IAAI,CAAC,AAAC,8BAA0D,OAA7B,KAAK,UAAU,CAAC,YAAY,EAAC;YACrE,KAAK,IAAI,CAAC,AAAC,0BAAoD,OAA3B,KAAK,UAAU,CAAC,UAAU;YAC9D,KAAK,IAAI,CAAC,AAAC,wBAAmE,OAA5C,KAAK,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,OAAM;YAE9E,KAAI,oCAAA,KAAK,UAAU,CAAC,gBAAgB,cAAhC,wDAAA,kCAAkC,eAAe,EAAE;gBACrD,KAAK,IAAI,CAAC,AAAC,6BAA6E,OAAjD,KAAK,UAAU,CAAC,gBAAgB,CAAC,eAAe,EAAC;YAC1F;YACA,KAAI,qCAAA,KAAK,UAAU,CAAC,gBAAgB,cAAhC,0DAAA,2CAAA,mCAAkC,MAAM,cAAxC,+DAAA,yCAA0C,KAAK,EAAE;gBACnD,KAAK,IAAI,CAAC,AAAC,4BAAyE,OAA9C,KAAK,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK;YACrF;QACF;QAEA,0BAA0B;QAC1B,KAAK,IAAI,CAAC,AAAC,4BAAwD,OAA7B,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM;QAClE,KAAK,IAAI,CAAC,AAAC,wBAAmF,OAA5D,KAAK,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,OAAO,CAAC,MAAM,OAAM;QAE9F,OAAO,KAAK,IAAI,CAAC;IACnB;IAEA,OAAe,cAAc,IAAsB,EAAU;QAC3D,MAAM,WAAqB,EAAE;QAE7B,SAAS;QACT,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,SAAS,IAAI,CAAC,AAAC,SAAsB,OAAd,KAAK,QAAQ;QACpC,SAAS,IAAI,CAAC,AAAC,cAAyD,OAA5C,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc;QACrE,SAAS,IAAI,CAAC;QAEd,WAAW;QACX,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YACjD,SAAS,IAAI,CAAC,AAAC,GAAiD,OAA/C,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,IAAG,MAAU,OAAN;QAClE;QACA,SAAS,IAAI,CAAC;QAEd,aAAa;QACb,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YACnD,MAAM,QAAQ,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;YACjF,SAAS,IAAI,CAAC,AAAC,GAAY,OAAV,OAAM,MAAU,OAAN;QAC7B;QACA,SAAS,IAAI,CAAC;QAEd,cAAc;QACd,IAAI,KAAK,UAAU,EAAE;YACnB,SAAS,IAAI,CAAC;YACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;YACzB,SAAS,IAAI,CAAC,AAAC,kBAA8C,OAA7B,KAAK,UAAU,CAAC,YAAY;YAC5D,SAAS,IAAI,CAAC,AAAC,eAA4D,OAA9C,CAAC,KAAK,UAAU,CAAC,UAAU,GAAG,GAAG,EAAE,OAAO,CAAC,IAAG;YAC3E,SAAS,IAAI,CAAC,AAAC,YAAmC,OAAxB,KAAK,UAAU,CAAC,OAAO;YACjD,SAAS,IAAI,CAAC;YAEd,IAAI,KAAK,UAAU,CAAC,gBAAgB,EAAE;oBAOhC,YACA,cACA;gBARJ,SAAS,IAAI,CAAC;gBACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;gBACzB,MAAM,KAAK,KAAK,UAAU,CAAC,gBAAgB;gBAC3C,IAAI,GAAG,eAAe,EAAE,SAAS,IAAI,CAAC,AAAC,iBAAmC,OAAnB,GAAG,eAAe;gBACzE,IAAI,GAAG,IAAI,EAAE,SAAS,IAAI,CAAC,AAAC,SAAgB,OAAR,GAAG,IAAI;gBAC3C,IAAI,GAAG,UAAU,EAAE,SAAS,IAAI,CAAC,AAAC,gBAA6B,OAAd,GAAG,UAAU;gBAC9D,KAAI,aAAA,GAAG,MAAM,cAAT,iCAAA,WAAW,IAAI,EAAE,SAAS,IAAI,CAAC,AAAC,WAAyB,OAAf,GAAG,MAAM,CAAC,IAAI;gBAC5D,KAAI,eAAA,GAAG,QAAQ,cAAX,mCAAA,aAAa,IAAI,EAAE,SAAS,IAAI,CAAC,AAAC,aAA6B,OAAjB,GAAG,QAAQ,CAAC,IAAI;gBAClE,KAAI,aAAA,GAAG,MAAM,cAAT,iCAAA,WAAW,KAAK,EAAE,SAAS,IAAI,CAAC,AAAC,UAA4B,OAAnB,GAAG,MAAM,CAAC,KAAK,EAAC,KAA4B,OAAzB,GAAG,MAAM,CAAC,QAAQ,IAAI;gBACvF,SAAS,IAAI,CAAC;YAChB;YAEA,IAAI,KAAK,UAAU,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG;gBAC1C,SAAS,IAAI,CAAC;gBACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;gBACzB,KAAK,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,SAAS;oBAC5C,SAAS,IAAI,CAAC,AAAC,GAAgB,OAAd,QAAQ,GAAE,MAAY,OAAR;gBACjC;gBACA,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,oBAAoB;QACpB,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;gBAAC,CAAC,KAAK,MAAM;YAC1D,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,SAAS,IAAI,CAAC,AAAC,GAAwB,OAAtB,IAAI,WAAW,IAAG,MAAiB,OAAb,MAAM,MAAM,EAAC;gBACpD,MAAM,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,MAAM;oBAC/B,SAAS,IAAI,CAAC,AAAC,KAAkB,OAAd,QAAQ,GAAE,MAA6B,OAAzB,KAAK,SAAS,CAAC,GAAG,MAAsC,OAA/B,KAAK,MAAM,GAAG,MAAM,QAAQ;gBACxF;gBACA,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,SAAS,IAAI,CAAC,AAAC,aAA6B,OAAjB,MAAM,MAAM,GAAG,GAAE;gBAC9C;gBACA,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,wBAAwB;QACxB,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,SAAS,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;QACjD,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM;YACvC,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,OAAe,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAQ;QACrF,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAS;QAClD,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,0BAA0B;QAC1B,IAAI,eAAe,CAAC;IACtB;AACF", "debugId": null}}, {"offset": {"line": 2329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport PDFUploader from '@/components/PDFUploader';\nimport PDFResults from '@/components/PDFResults';\nimport { PDFExtractorService } from '@/services/pdfExtractor';\nimport { ExportService } from '@/services/exportService';\nimport { ExtractedPDFData } from '@/types/pdf';\nimport { FileText, Zap, Shield, Download } from 'lucide-react';\n\nexport default function Home() {\n  const [extractedData, setExtractedData] = useState<ExtractedPDFData | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleFileUpload = async (file: File) => {\n    setIsProcessing(true);\n    setError(null);\n    setExtractedData(null);\n\n    try {\n      const data = await PDFExtractorService.extractPDFData(file);\n      setExtractedData(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred while processing the PDF');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleExport = (format: 'json' | 'csv' | 'txt') => {\n    if (!extractedData) return;\n\n    switch (format) {\n      case 'json':\n        ExportService.exportAsJSON(extractedData);\n        break;\n      case 'csv':\n        ExportService.exportAsCSV(extractedData);\n        break;\n      case 'txt':\n        ExportService.exportAsText(extractedData);\n        break;\n    }\n  };\n\n  const resetApp = () => {\n    setExtractedData(null);\n    setError(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"bg-blue-600 p-2 rounded-lg\">\n                <FileText className=\"h-8 w-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">PDF Info Extractor</h1>\n                <p className=\"text-gray-600\">Extract structured information from PDF documents</p>\n              </div>\n            </div>\n            {extractedData && (\n              <button\n                onClick={resetApp}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n              >\n                New PDF\n              </button>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!extractedData && !isProcessing && (\n          <>\n            {/* Features Section */}\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Most Effective PDF Information Extractor\n              </h2>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-3xl mx-auto\">\n                Upload any PDF document and get comprehensive, structured information including metadata,\n                content analysis, and detailed statistics in seconds.\n              </p>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n                <FeatureCard\n                  icon={Zap}\n                  title=\"Lightning Fast\"\n                  description=\"Extract information from PDFs in seconds with our optimized processing engine\"\n                />\n                <FeatureCard\n                  icon={Shield}\n                  title=\"Secure & Private\"\n                  description=\"All processing happens locally in your browser. Your files never leave your device\"\n                />\n                <FeatureCard\n                  icon={Download}\n                  title=\"Multiple Formats\"\n                  description=\"Export extracted data as JSON, CSV, or formatted text for further analysis\"\n                />\n              </div>\n            </div>\n\n            {/* Upload Section */}\n            <PDFUploader onFileUpload={handleFileUpload} isProcessing={isProcessing} />\n          </>\n        )}\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"max-w-2xl mx-auto mb-8\">\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"flex\">\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                  <div className=\"mt-2 text-sm text-red-700\">\n                    <p>{error}</p>\n                  </div>\n                  <div className=\"mt-4\">\n                    <button\n                      onClick={resetApp}\n                      className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 transition-colors\"\n                    >\n                      Try Again\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Results */}\n        {extractedData && (\n          <PDFResults data={extractedData} onExport={handleExport} />\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>Built with Next.js, TypeScript, and Tailwind CSS</p>\n            <p className=\"mt-2 text-sm\">Secure, fast, and privacy-focused PDF information extraction</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\n// Feature Card Component\nfunction FeatureCard({\n  icon: Icon,\n  title,\n  description\n}: {\n  icon: any,\n  title: string,\n  description: string\n}) {\n  return (\n    <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n      <div className=\"bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4\">\n        <Icon className=\"h-6 w-6 text-blue-600\" />\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 text-sm\">{description}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,SAAS;QACT,iBAAiB;QAEjB,IAAI;YACF,MAAM,OAAO,MAAM,kIAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC;YACtD,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,eAAe;QAEpB,OAAQ;YACN,KAAK;gBACH,mIAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;gBAC3B;YACF,KAAK;gBACH,mIAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;gBAC1B;YACF,KAAK;gBACH,mIAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;gBAC3B;QACJ;IACF;IAEA,MAAM,WAAW;QACf,iBAAiB;QACjB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;4BAGhC,+BACC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;;oBACb,CAAC,iBAAiB,CAAC,8BAClB;;0CAEE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAK5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAM,mMAAA,CAAA,MAAG;gDACT,OAAM;gDACN,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAM,yMAAA,CAAA,SAAM;gDACZ,OAAM;gDACN,aAAY;;;;;;0DAEd,6LAAC;gDACC,MAAM,6MAAA,CAAA,WAAQ;gDACd,OAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;;0CAMlB,6LAAC,oIAAA,CAAA,UAAW;gCAAC,cAAc;gCAAkB,cAAc;;;;;;;;oBAK9D,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAG;;;;;;;;;;;sDAEN,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWZ,+BACC,6LAAC,mIAAA,CAAA,UAAU;wBAAC,MAAM;wBAAe,UAAU;;;;;;;;;;;;0BAK/C,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GAnJwB;KAAA;AAqJxB,yBAAyB;AACzB,SAAS,YAAY,KAQpB;QARoB,EACnB,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EAKZ,GARoB;IASnB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,6LAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,6LAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C;MAlBS", "debugId": null}}]}