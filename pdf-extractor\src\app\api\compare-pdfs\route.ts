import { NextRequest, NextResponse } from 'next/server';
import { ExtractedPDFData, PDFComparison } from '@/types/pdf';
import { PDFComparator } from '@/services/pdfComparator';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { documents } = body;

    if (!documents || !Array.isArray(documents) || documents.length < 2) {
      return NextResponse.json(
        { error: 'At least 2 documents are required for comparison' },
        { status: 400 }
      );
    }

    if (documents.length > 5) {
      return NextResponse.json(
        { error: 'Maximum 5 documents can be compared at once' },
        { status: 400 }
      );
    }

    // Validate document structure
    for (const doc of documents) {
      if (!doc.id || !doc.fileName || !doc.content) {
        return NextResponse.json(
          { error: 'Invalid document structure' },
          { status: 400 }
        );
      }
    }

    const comparison = await PDFComparator.comparePDFs(documents);
    
    return NextResponse.json(comparison);
  } catch (error) {
    console.error('PDF comparison error:', error);
    return NextResponse.json(
      { error: 'Failed to compare PDFs. Please try again.' },
      { status: 500 }
    );
  }
}
