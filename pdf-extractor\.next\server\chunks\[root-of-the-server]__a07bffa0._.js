module.exports = {

"[project]/.next-internal/server/app/api/detailed-analysis/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/services/pdfComparator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PDFComparator": ()=>PDFComparator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
class PDFComparator {
    static async comparePDFs(documents) {
        try {
            if (documents.length < 2) {
                throw new Error('At least 2 documents are required for comparison');
            }
            const comparisonAnalysis = await this.performAIComparison(documents);
            return {
                id: this.generateId(),
                documents,
                differences: comparisonAnalysis.differences,
                summary: comparisonAnalysis.summary,
                recommendations: comparisonAnalysis.recommendations,
                comparedAt: new Date().toISOString(),
                comparisonType: this.detectComparisonType(documents)
            };
        } catch (error) {
            console.error('PDF comparison error:', error);
            throw error;
        }
    }
    static async performAIComparison(documents) {
        try {
            const prompt = this.createComparisonPrompt(documents);
            const completion = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert document comparison analyst specializing in business documents, quotations, contracts, and proposals. Compare the provided documents and identify key differences, focusing on pricing, terms, vendors, timelines, and other critical business factors."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 3000
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from OpenAI');
            }
            const analysis = JSON.parse(response);
            return this.validateComparisonAnalysis(analysis);
        } catch (error) {
            console.error('AI comparison error:', error);
            return this.createFallbackComparison(documents);
        }
    }
    static createComparisonPrompt(documents) {
        const documentSummaries = documents.map((doc, index)=>{
            const quotationDetails = doc.aiAnalysis?.quotationDetails;
            return `
Document ${index + 1}: ${doc.fileName}
- Document Type: ${doc.aiAnalysis?.documentType || 'unknown'}
- Content Preview: ${doc.content.fullText.substring(0, 1000)}...
- Quotation Details: ${quotationDetails ? JSON.stringify(quotationDetails, null, 2) : 'None extracted'}
- Key Insights: ${doc.aiAnalysis?.keyInsights?.join('; ') || 'None'}
`;
        }).join('\n');
        return `
Compare the following ${documents.length} business documents and provide a detailed analysis in JSON format.

${documentSummaries}

Please provide analysis in this exact JSON structure:
{
  "differences": [
    {
      "field": "pricing|vendor|terms|timeline|other",
      "type": "added|removed|changed|same",
      "oldValue": "value from first document or null",
      "newValue": "value from second document or null", 
      "significance": "high|medium|low",
      "description": "Clear description of the difference"
    }
  ],
  "summary": "Overall summary of key differences between documents",
  "recommendations": [
    "Actionable recommendations based on the comparison"
  ]
}

Focus on comparing:
1. PRICING: Total costs, line item prices, discounts, taxes
2. VENDORS: Company names, contact information, credentials
3. TERMS: Payment terms, delivery dates, warranties, conditions
4. TIMELINE: Project duration, milestones, deadlines
5. SCOPE: Services/products included, specifications, quantities
6. RISKS: Potential issues, missing information, unclear terms

For each difference, assess:
- Business impact (high/medium/low significance)
- Financial implications
- Risk factors
- Recommendations for decision making

Return only valid JSON without markdown formatting.
`;
    }
    static validateComparisonAnalysis(analysis) {
        return {
            differences: Array.isArray(analysis.differences) ? analysis.differences.map((diff)=>({
                    field: diff.field || 'other',
                    type: diff.type || 'changed',
                    oldValue: diff.oldValue,
                    newValue: diff.newValue,
                    significance: diff.significance || 'medium',
                    description: diff.description || 'No description available'
                })) : [],
            summary: analysis.summary || 'Comparison completed',
            recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : []
        };
    }
    static createFallbackComparison(documents) {
        const differences = [];
        // Basic comparison of document types
        const docTypes = documents.map((d)=>d.aiAnalysis?.documentType || 'unknown');
        if (new Set(docTypes).size > 1) {
            differences.push({
                field: 'document_type',
                type: 'changed',
                oldValue: docTypes[0],
                newValue: docTypes[1],
                significance: 'medium',
                description: `Document types differ: ${docTypes.join(' vs ')}`
            });
        }
        // Basic comparison of file sizes
        const sizes = documents.map((d)=>d.statistics.totalCharacters);
        const sizeDiff = Math.abs(sizes[0] - sizes[1]) / Math.max(sizes[0], sizes[1]);
        if (sizeDiff > 0.2) {
            differences.push({
                field: 'content_length',
                type: 'changed',
                oldValue: sizes[0],
                newValue: sizes[1],
                significance: 'low',
                description: `Significant difference in document length: ${sizeDiff > 0.5 ? 'major' : 'moderate'} variation`
            });
        }
        return {
            differences,
            summary: `Basic comparison of ${documents.length} documents completed. AI analysis unavailable.`,
            recommendations: [
                'Review documents manually for detailed comparison',
                'Check pricing and terms carefully',
                'Verify vendor information and credentials'
            ]
        };
    }
    static detectComparisonType(documents) {
        const types = documents.map((d)=>d.aiAnalysis?.documentType).filter(Boolean);
        if (types.every((type)=>type === 'quotation')) return 'quotation';
        if (types.every((type)=>type === 'contract')) return 'contract';
        if (types.every((type)=>type === 'invoice')) return 'invoice';
        return 'general';
    }
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // Generate side-by-side comparison with scoring
    static async generateSideBySideComparison(documents) {
        try {
            const scores = await this.scoreQuotations(documents);
            const comparisonMatrix = this.buildComparisonMatrix(documents);
            const bestChoice = this.determineBestChoice(scores);
            return {
                documents,
                scores,
                bestChoice,
                comparisonMatrix
            };
        } catch (error) {
            console.error('Side-by-side comparison error:', error);
            return this.createFallbackSideBySide(documents);
        }
    }
    static async scoreQuotations(documents) {
        try {
            const prompt = this.createScoringPrompt(documents);
            const completion = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert procurement analyst. Score and evaluate quotations based on pricing, terms, vendor reliability, timeline, and completeness. Provide detailed scoring and recommendations."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2500
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from OpenAI');
            }
            const analysis = JSON.parse(response);
            return this.validateQuotationScores(analysis.scores, documents);
        } catch (error) {
            console.error('Quotation scoring error:', error);
            return this.createFallbackScores(documents);
        }
    }
    static createScoringPrompt(documents) {
        const documentSummaries = documents.map((doc, index)=>{
            const quotationDetails = doc.aiAnalysis?.quotationDetails;
            return `
Document ${index + 1}: ${doc.fileName}
- Total Amount: ${quotationDetails?.totals?.total || 'Not specified'} ${quotationDetails?.totals?.currency || ''}
- Vendor: ${quotationDetails?.vendor?.name || 'Not specified'}
- Valid Until: ${quotationDetails?.validUntil || 'Not specified'}
- Items Count: ${quotationDetails?.items?.length || 0}
- Terms: ${quotationDetails?.terms?.join('; ') || 'Not specified'}
- Content Quality: ${doc.statistics.totalWords} words, ${doc.statistics.totalPages} pages
`;
        }).join('\n');
        return `
Score and evaluate the following ${documents.length} quotations for a procurement decision.

${documentSummaries}

Provide scoring in this exact JSON format:
{
  "scores": [
    {
      "documentId": "document_id",
      "fileName": "filename",
      "totalScore": 85,
      "scores": {
        "pricing": 90,
        "terms": 80,
        "vendor": 85,
        "timeline": 85,
        "completeness": 90
      },
      "strengths": ["Lower total cost", "Flexible payment terms", "Fast delivery"],
      "weaknesses": ["Unknown vendor reputation", "Limited warranty"],
      "recommendation": "best|good|acceptable|poor"
    }
  ]
}

Scoring criteria (0-100):
- PRICING: Lower premium + higher liability limits + lower deductible = higher score
- TERMS: Better policy terms, broader territory, favorable jurisdiction = higher score
- VENDOR: Insurer reputation, financial strength, claims handling = higher score
- TIMELINE: Policy period, retroactive coverage = higher score
- COMPLETENESS: Comprehensive coverage, fewer exclusions = higher score

Calculate totalScore as weighted average: pricing(35%) + terms(25%) + vendor(20%) + timeline(10%) + completeness(10%)

Focus on insurance-specific factors:
- Premium competitiveness vs coverage provided
- Liability limits adequacy
- Deductible levels
- Coverage extensions included
- Major exclusions present
- Policy territory and jurisdiction
- Insurer financial strength

Return only valid JSON without markdown formatting.
`;
    }
    // Helper method to get detailed comparison analysis
    static async getDetailedAnalysis(comparison) {
        const differences = comparison.differences;
        // Generate side-by-side comparison if quotation type
        let sideBySide;
        if (comparison.comparisonType === 'quotation' && comparison.documents.length >= 2) {
            sideBySide = await this.generateSideBySideComparison(comparison.documents);
        }
        return {
            pricing: {
                differences: differences.filter((d)=>d.field.includes('pricing') || d.field.includes('cost') || d.field.includes('total')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('pricing')))
            },
            terms: {
                differences: differences.filter((d)=>d.field.includes('terms') || d.field.includes('condition')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('terms')))
            },
            vendors: {
                differences: differences.filter((d)=>d.field.includes('vendor') || d.field.includes('supplier')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('vendor')))
            },
            timeline: {
                differences: differences.filter((d)=>d.field.includes('date') || d.field.includes('timeline') || d.field.includes('delivery')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('date')))
            },
            overall: {
                recommendation: comparison.summary,
                bestOption: this.determineBestOption(comparison),
                riskFactors: this.identifyRiskFactors(comparison)
            },
            sideBySide
        };
    }
    static summarizeDifferences(differences) {
        if (differences.length === 0) return 'No significant differences found';
        const highImpact = differences.filter((d)=>d.significance === 'high').length;
        const mediumImpact = differences.filter((d)=>d.significance === 'medium').length;
        return `Found ${differences.length} difference(s): ${highImpact} high impact, ${mediumImpact} medium impact`;
    }
    static determineBestOption(comparison) {
        // Simple logic to determine best option based on differences
        const highImpactDiffs = comparison.differences.filter((d)=>d.significance === 'high');
        if (highImpactDiffs.length === 0) {
            return 'Options are comparable - review based on your priorities';
        }
        return 'Manual review recommended due to significant differences';
    }
    static identifyRiskFactors(comparison) {
        const risks = [];
        const highImpactDiffs = comparison.differences.filter((d)=>d.significance === 'high');
        if (highImpactDiffs.length > 0) {
            risks.push(`${highImpactDiffs.length} high-impact differences require attention`);
        }
        const pricingDiffs = comparison.differences.filter((d)=>d.field.includes('pricing'));
        if (pricingDiffs.length > 0) {
            risks.push('Pricing variations detected - verify total costs');
        }
        return risks;
    }
    static validateQuotationScores(scores, documents) {
        return documents.map((doc, index)=>{
            const score = scores[index] || {};
            return {
                documentId: doc.id,
                fileName: doc.fileName,
                totalScore: Math.min(Math.max(score.totalScore || 50, 0), 100),
                scores: {
                    pricing: Math.min(Math.max(score.scores?.pricing || 50, 0), 100),
                    terms: Math.min(Math.max(score.scores?.terms || 50, 0), 100),
                    vendor: Math.min(Math.max(score.scores?.vendor || 50, 0), 100),
                    timeline: Math.min(Math.max(score.scores?.timeline || 50, 0), 100),
                    completeness: Math.min(Math.max(score.scores?.completeness || 50, 0), 100)
                },
                strengths: Array.isArray(score.strengths) ? score.strengths : [
                    'Analysis pending'
                ],
                weaknesses: Array.isArray(score.weaknesses) ? score.weaknesses : [
                    'Analysis pending'
                ],
                recommendation: [
                    'best',
                    'good',
                    'acceptable',
                    'poor'
                ].includes(score.recommendation) ? score.recommendation : 'acceptable'
            };
        });
    }
    static createFallbackScores(documents) {
        return documents.map((doc, index)=>({
                documentId: doc.id,
                fileName: doc.fileName,
                totalScore: 70 - index * 5,
                scores: {
                    pricing: 70,
                    terms: 70,
                    vendor: 70,
                    timeline: 70,
                    completeness: 70
                },
                strengths: [
                    'Document processed successfully'
                ],
                weaknesses: [
                    'AI analysis unavailable'
                ],
                recommendation: index === 0 ? 'good' : 'acceptable'
            }));
    }
    static buildComparisonMatrix(documents) {
        const matrix = [];
        // Insurance policy specific fields for comparison
        const fields = [
            // Core Policy Terms
            {
                key: 'limit_of_liability',
                category: 'pricing',
                label: 'Limit of Liability'
            },
            {
                key: 'annual_premium',
                category: 'pricing',
                label: 'Annual Premium (GST 18% additional)'
            },
            {
                key: 'deductible',
                category: 'pricing',
                label: 'Deductible (each and every claim)'
            },
            {
                key: 'retroactive_date',
                category: 'terms',
                label: 'Retroactive Date'
            },
            {
                key: 'territory',
                category: 'terms',
                label: 'Territory'
            },
            {
                key: 'jurisdiction',
                category: 'terms',
                label: 'Jurisdiction'
            },
            {
                key: 'wording',
                category: 'terms',
                label: 'Wording'
            },
            // Broad Coverage Extensions
            {
                key: 'fake_president_fraud',
                category: 'coverage',
                label: 'Fake President Fraud Extension / Social Engineering Fraud/ Impersonation'
            },
            {
                key: 'additional_cost_cover',
                category: 'coverage',
                label: 'Additional Cost Cover'
            },
            {
                key: 'property_damage_robbery',
                category: 'coverage',
                label: 'Loss or Damage to property/premises by Robbery/ Criminal Damage'
            },
            {
                key: 'safe_burglary',
                category: 'coverage',
                label: 'Loss or damage to property within safe from safe burglary/ Violent and forcible'
            },
            {
                key: 'computer_fraud',
                category: 'coverage',
                label: 'Computer Fraud cover'
            },
            {
                key: 'loss_third_party',
                category: 'coverage',
                label: 'Loss to Third Party'
            },
            {
                key: 'contractual_penalties',
                category: 'coverage',
                label: 'Contractual Penalties/ Expectation Damages'
            },
            {
                key: 'credit_card_fraud',
                category: 'coverage',
                label: 'Credit Card Fraud Cover'
            },
            {
                key: 'funds_transfer_fraud',
                category: 'coverage',
                label: 'Funds Transfer Fraud coverage'
            },
            {
                key: 'counterfeit_currency',
                category: 'coverage',
                label: 'Counterfeit Currency Fraud Cover'
            },
            {
                key: 'money_securities',
                category: 'coverage',
                label: 'Money and securities - Damage, destruction and disappearance cover/ Premises Coverage'
            },
            {
                key: 'legal_expenses',
                category: 'coverage',
                label: 'Legal Expenses / Fees, costs and expenses'
            },
            {
                key: 'investigative_costs',
                category: 'coverage',
                label: 'Investigative costs / Investigation Specialist costs'
            },
            {
                key: 'reconstitution_costs',
                category: 'coverage',
                label: 'Reconstitution/ Restoration Costs'
            },
            {
                key: 'care_custody_control',
                category: 'coverage',
                label: 'Care, Custody or Control'
            },
            {
                key: 'auto_acquisition',
                category: 'coverage',
                label: 'Auto acquisition of subsidiaries'
            },
            {
                key: 'interest_receivable',
                category: 'coverage',
                label: 'Interest receivable or Payable'
            },
            {
                key: 'court_attendance',
                category: 'coverage',
                label: 'Court Attendance costs'
            },
            {
                key: 'audit_fee',
                category: 'coverage',
                label: 'Audit Fee'
            },
            {
                key: 'control_group_clause',
                category: 'coverage',
                label: 'Control Group Clause'
            },
            {
                key: 'other_coverages',
                category: 'coverage',
                label: 'Other Coverages'
            },
            // Major Exclusions
            {
                key: 'known_acts_claims',
                category: 'exclusions',
                label: 'Known Acts/ Claims'
            },
            {
                key: 'consequential_loss',
                category: 'exclusions',
                label: 'Consequential Loss'
            },
            {
                key: 'fines_penalties',
                category: 'exclusions',
                label: 'Fines & penalties'
            }
        ];
        fields.forEach((field)=>{
            const values = documents.map((doc)=>{
                const fullText = doc.content.fullText.toLowerCase();
                let value = 'Not specified';
                // Extract insurance-specific fields using text analysis
                switch(field.key){
                    case 'limit_of_liability':
                        value = this.extractInsuranceField(fullText, [
                            'limit of liability',
                            'liability limit',
                            'aggregate limit'
                        ]) || 'Not specified';
                        break;
                    case 'annual_premium':
                        value = this.extractInsuranceField(fullText, [
                            'annual premium',
                            'premium',
                            'total premium'
                        ]) || 'Not specified';
                        break;
                    case 'deductible':
                        value = this.extractInsuranceField(fullText, [
                            'deductible',
                            'excess',
                            'retention'
                        ]) || 'Not specified';
                        break;
                    case 'retroactive_date':
                        value = this.extractInsuranceField(fullText, [
                            'retroactive date',
                            'retroactive'
                        ]) || 'Not specified';
                        break;
                    case 'territory':
                        value = this.extractInsuranceField(fullText, [
                            'territory',
                            'geographical limit',
                            'coverage area'
                        ]) || 'Not specified';
                        break;
                    case 'jurisdiction':
                        value = this.extractInsuranceField(fullText, [
                            'jurisdiction',
                            'governing law',
                            'legal jurisdiction'
                        ]) || 'Not specified';
                        break;
                    case 'wording':
                        value = this.extractInsuranceField(fullText, [
                            'wording',
                            'policy wording',
                            'terms and conditions'
                        ]) || 'Not specified';
                        break;
                    // Coverage Extensions
                    case 'fake_president_fraud':
                        value = this.checkCoverageIncluded(fullText, [
                            'fake president',
                            'social engineering',
                            'impersonation fraud'
                        ]);
                        break;
                    case 'additional_cost_cover':
                        value = this.checkCoverageIncluded(fullText, [
                            'additional cost',
                            'extra expense'
                        ]);
                        break;
                    case 'property_damage_robbery':
                        value = this.checkCoverageIncluded(fullText, [
                            'robbery',
                            'criminal damage',
                            'property damage'
                        ]);
                        break;
                    case 'safe_burglary':
                        value = this.checkCoverageIncluded(fullText, [
                            'safe burglary',
                            'violent and forcible'
                        ]);
                        break;
                    case 'computer_fraud':
                        value = this.checkCoverageIncluded(fullText, [
                            'computer fraud',
                            'cyber fraud'
                        ]);
                        break;
                    case 'loss_third_party':
                        value = this.checkCoverageIncluded(fullText, [
                            'third party',
                            'loss to third party'
                        ]);
                        break;
                    case 'contractual_penalties':
                        value = this.checkCoverageIncluded(fullText, [
                            'contractual penalties',
                            'expectation damages'
                        ]);
                        break;
                    case 'credit_card_fraud':
                        value = this.checkCoverageIncluded(fullText, [
                            'credit card fraud',
                            'card fraud'
                        ]);
                        break;
                    case 'funds_transfer_fraud':
                        value = this.checkCoverageIncluded(fullText, [
                            'funds transfer fraud',
                            'wire fraud'
                        ]);
                        break;
                    case 'counterfeit_currency':
                        value = this.checkCoverageIncluded(fullText, [
                            'counterfeit currency',
                            'fake currency'
                        ]);
                        break;
                    case 'money_securities':
                        value = this.checkCoverageIncluded(fullText, [
                            'money and securities',
                            'premises coverage'
                        ]);
                        break;
                    case 'legal_expenses':
                        value = this.checkCoverageIncluded(fullText, [
                            'legal expenses',
                            'legal fees'
                        ]);
                        break;
                    case 'investigative_costs':
                        value = this.checkCoverageIncluded(fullText, [
                            'investigative costs',
                            'investigation specialist'
                        ]);
                        break;
                    case 'reconstitution_costs':
                        value = this.checkCoverageIncluded(fullText, [
                            'reconstitution',
                            'restoration costs'
                        ]);
                        break;
                    case 'care_custody_control':
                        value = this.checkCoverageIncluded(fullText, [
                            'care custody control',
                            'care, custody or control'
                        ]);
                        break;
                    case 'auto_acquisition':
                        value = this.checkCoverageIncluded(fullText, [
                            'auto acquisition',
                            'automatic acquisition'
                        ]);
                        break;
                    case 'interest_receivable':
                        value = this.checkCoverageIncluded(fullText, [
                            'interest receivable',
                            'interest payable'
                        ]);
                        break;
                    case 'court_attendance':
                        value = this.checkCoverageIncluded(fullText, [
                            'court attendance',
                            'court costs'
                        ]);
                        break;
                    case 'audit_fee':
                        value = this.checkCoverageIncluded(fullText, [
                            'audit fee',
                            'audit costs'
                        ]);
                        break;
                    case 'control_group_clause':
                        value = this.checkCoverageIncluded(fullText, [
                            'control group',
                            'control group clause'
                        ]);
                        break;
                    case 'other_coverages':
                        value = this.checkCoverageIncluded(fullText, [
                            'other coverages',
                            'additional coverages'
                        ]);
                        break;
                    // Exclusions
                    case 'known_acts_claims':
                        value = this.checkExclusionPresent(fullText, [
                            'known acts',
                            'known claims'
                        ]);
                        break;
                    case 'consequential_loss':
                        value = this.checkExclusionPresent(fullText, [
                            'consequential loss',
                            'indirect loss'
                        ]);
                        break;
                    case 'fines_penalties':
                        value = this.checkExclusionPresent(fullText, [
                            'fines',
                            'penalties',
                            'fines and penalties'
                        ]);
                        break;
                }
                return {
                    documentId: doc.id,
                    value,
                    isBest: false,
                    isWorst: false,
                    score: 70 // Default score
                };
            });
            // Determine best/worst for this field based on category
            if (field.category === 'pricing') {
                // For pricing fields, extract and compare numerical values
                const amounts = values.map((v)=>{
                    const match = v.value.match(/[\d,]+\.?\d*/);
                    return match ? parseFloat(match[0].replace(/,/g, '')) : Infinity;
                });
                const minAmount = Math.min(...amounts);
                const maxAmount = Math.max(...amounts);
                values.forEach((v, i)=>{
                    if (field.key === 'deductible') {
                        // For deductible, lower is better
                        if (amounts[i] === minAmount && minAmount !== Infinity) v.isBest = true;
                        if (amounts[i] === maxAmount && maxAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;
                    } else if (field.key === 'limit_of_liability') {
                        // For liability limit, higher is better
                        if (amounts[i] === maxAmount && maxAmount !== Infinity) v.isBest = true;
                        if (amounts[i] === minAmount && minAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;
                    } else {
                        // For premium, lower is better
                        if (amounts[i] === minAmount && minAmount !== Infinity) v.isBest = true;
                        if (amounts[i] === maxAmount && maxAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;
                    }
                    v.score = amounts[i] === Infinity ? 50 : Math.round(100 - (amounts[i] - minAmount) / (maxAmount - minAmount) * 50);
                });
            } else if (field.category === 'coverage') {
                // For coverage, "Included" is better than "Not specified"
                values.forEach((v)=>{
                    if (v.value === 'Included') {
                        v.isBest = true;
                        v.score = 100;
                    } else if (v.value === 'Excluded') {
                        v.isWorst = true;
                        v.score = 20;
                    } else {
                        v.score = 50;
                    }
                });
            } else if (field.category === 'exclusions') {
                // For exclusions, "Not present" is better than "Present"
                values.forEach((v)=>{
                    if (v.value === 'Not present') {
                        v.isBest = true;
                        v.score = 100;
                    } else if (v.value === 'Present') {
                        v.isWorst = true;
                        v.score = 20;
                    } else {
                        v.score = 50;
                    }
                });
            } else {
                // For terms, mark first non-empty as best
                const firstValid = values.find((v)=>v.value !== 'Not specified');
                if (firstValid) firstValid.isBest = true;
                values.forEach((v)=>{
                    v.score = v.value !== 'Not specified' ? 80 : 30;
                });
            }
            matrix.push({
                field: field.label,
                category: field.category,
                values
            });
        });
        return matrix;
    }
    static determineBestChoice(scores) {
        const bestScore = scores.reduce((best, current)=>current.totalScore > best.totalScore ? current : best);
        return {
            documentId: bestScore.documentId,
            reason: `Highest overall score with strong performance in ${bestScore.strengths.slice(0, 2).join(' and ')}`,
            confidence: Math.min(bestScore.totalScore / 100, 0.95)
        };
    }
    static createFallbackSideBySide(documents) {
        const scores = this.createFallbackScores(documents);
        const comparisonMatrix = this.buildComparisonMatrix(documents);
        const bestChoice = this.determineBestChoice(scores);
        return {
            documents,
            scores,
            bestChoice,
            comparisonMatrix
        };
    }
    // Helper methods for insurance field extraction
    static extractInsuranceField(text, keywords) {
        for (const keyword of keywords){
            const regex = new RegExp(`${keyword}[:\\s]*([^\\n\\r]{1,200})`, 'i');
            const match = text.match(regex);
            if (match && match[1]) {
                return match[1].trim().replace(/[^\w\s\d,.-]/g, '').substring(0, 100);
            }
        }
        return null;
    }
    static checkCoverageIncluded(text, keywords) {
        for (const keyword of keywords){
            if (text.includes(keyword.toLowerCase())) {
                // Check if it's mentioned as included or covered
                const context = this.getContextAroundKeyword(text, keyword, 50);
                if (context.includes('included') || context.includes('covered') || context.includes('yes')) {
                    return 'Included';
                } else if (context.includes('excluded') || context.includes('not covered') || context.includes('no')) {
                    return 'Excluded';
                } else {
                    return 'Mentioned';
                }
            }
        }
        return 'Not specified';
    }
    static checkExclusionPresent(text, keywords) {
        for (const keyword of keywords){
            if (text.includes(keyword.toLowerCase())) {
                const context = this.getContextAroundKeyword(text, keyword, 50);
                if (context.includes('excluded') || context.includes('not covered') || context.includes('exclusion')) {
                    return 'Present';
                } else {
                    return 'Mentioned';
                }
            }
        }
        return 'Not present';
    }
    static getContextAroundKeyword(text, keyword, contextLength) {
        const index = text.toLowerCase().indexOf(keyword.toLowerCase());
        if (index === -1) return '';
        const start = Math.max(0, index - contextLength);
        const end = Math.min(text.length, index + keyword.length + contextLength);
        return text.substring(start, end).toLowerCase();
    }
}
}),
"[project]/src/app/api/detailed-analysis/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$pdfComparator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/pdfComparator.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { comparison } = body;
        if (!comparison || !comparison.documents) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid comparison data'
            }, {
                status: 400
            });
        }
        const detailedAnalysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$pdfComparator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PDFComparator"].getDetailedAnalysis(comparison);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(detailedAnalysis);
    } catch (error) {
        console.error('Detailed analysis error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate detailed analysis'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a07bffa0._.js.map