import OpenAI from 'openai';
import { AIAnalysis, QuotationDetails } from '@/types/pdf';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export class AIAnalyzer {
  static async analyzeDocument(text: string, fileName: string): Promise<AIAnalysis> {
    try {
      const prompt = this.createAnalysisPrompt(text, fileName);
      
      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "You are an expert document analyzer specializing in business documents, quotations, invoices, and contracts. Analyze the provided text and extract structured information in JSON format."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      // Parse the JSON response
      const analysis = JSON.parse(response);
      return this.validateAndFormatAnalysis(analysis);
    } catch (error) {
      console.error('AI Analysis error:', error);
      return this.createFallbackAnalysis(text);
    }
  }

  private static createAnalysisPrompt(text: string, fileName: string): string {
    return `
Analyze the following document text and provide a comprehensive analysis in JSON format.

Document Name: ${fileName}
Document Text:
${text.substring(0, 4000)} ${text.length > 4000 ? '...(truncated)' : ''}

Please provide analysis in this exact JSON structure:
{
  "documentType": "quotation|invoice|contract|proposal|other",
  "confidence": 0.95,
  "quotationDetails": {
    "quotationNumber": "string or null",
    "date": "YYYY-MM-DD or null",
    "validUntil": "YYYY-MM-DD or null",
    "vendor": {
      "name": "string or null",
      "address": "string or null",
      "contact": "string or null",
      "email": "string or null",
      "phone": "string or null"
    },
    "customer": {
      "name": "string or null",
      "address": "string or null",
      "contact": "string or null",
      "email": "string or null",
      "phone": "string or null"
    },
    "items": [
      {
        "description": "string",
        "quantity": number,
        "unitPrice": number,
        "totalPrice": number,
        "unit": "string"
      }
    ],
    "totals": {
      "subtotal": number,
      "tax": number,
      "discount": number,
      "total": number,
      "currency": "USD|EUR|GBP|etc"
    },
    "terms": ["array of terms and conditions"],
    "notes": "additional notes"
  },
  "summary": "Brief summary of the document",
  "keyInsights": ["array of key insights"],
  "recommendations": ["array of recommendations"],
  "extractedEntities": {
    "dates": ["array of dates found"],
    "amounts": ["array of monetary amounts"],
    "companies": ["array of company names"],
    "contacts": ["array of contact information"]
  }
}

Focus on:
1. Identifying if this is a quotation/quote document
2. Extracting all pricing information, line items, and totals
3. Finding vendor and customer details
4. Identifying key dates (quote date, validity, delivery dates)
5. Extracting terms and conditions
6. Providing actionable insights about the quotation

Return only valid JSON without any markdown formatting or additional text.
`;
  }

  private static validateAndFormatAnalysis(analysis: any): AIAnalysis {
    return {
      documentType: analysis.documentType || 'unknown',
      confidence: Math.min(Math.max(analysis.confidence || 0, 0), 1),
      quotationDetails: analysis.quotationDetails || undefined,
      summary: analysis.summary || 'No summary available',
      keyInsights: Array.isArray(analysis.keyInsights) ? analysis.keyInsights : [],
      recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : undefined,
      extractedEntities: {
        dates: Array.isArray(analysis.extractedEntities?.dates) ? analysis.extractedEntities.dates : [],
        amounts: Array.isArray(analysis.extractedEntities?.amounts) ? analysis.extractedEntities.amounts : [],
        companies: Array.isArray(analysis.extractedEntities?.companies) ? analysis.extractedEntities.companies : [],
        contacts: Array.isArray(analysis.extractedEntities?.contacts) ? analysis.extractedEntities.contacts : [],
      }
    };
  }

  private static createFallbackAnalysis(text: string): AIAnalysis {
    // Simple fallback analysis using basic text processing
    const words = text.toLowerCase();
    const isQuotation = words.includes('quotation') || words.includes('quote') || words.includes('estimate');
    const isInvoice = words.includes('invoice') || words.includes('bill');
    const isContract = words.includes('contract') || words.includes('agreement');
    
    let documentType = 'other';
    if (isQuotation) documentType = 'quotation';
    else if (isInvoice) documentType = 'invoice';
    else if (isContract) documentType = 'contract';

    // Extract basic entities
    const dateRegex = /\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}|\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2}/g;
    const amountRegex = /\$[\d,]+\.?\d*|[\d,]+\.?\d*\s*(?:USD|EUR|GBP|\$)/gi;
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;

    const dates = text.match(dateRegex) || [];
    const amounts = text.match(amountRegex) || [];
    const emails = text.match(emailRegex) || [];

    return {
      documentType,
      confidence: 0.6,
      summary: `Document appears to be a ${documentType}. Basic analysis completed due to AI service unavailability.`,
      keyInsights: [
        `Document type identified as: ${documentType}`,
        `Found ${dates.length} date references`,
        `Found ${amounts.length} monetary amounts`,
        `Found ${emails.length} email addresses`
      ],
      extractedEntities: {
        dates,
        amounts,
        companies: [],
        contacts: emails
      }
    };
  }
}
