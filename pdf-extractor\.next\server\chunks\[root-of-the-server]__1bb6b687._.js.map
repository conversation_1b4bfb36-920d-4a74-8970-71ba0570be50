{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/api/test-pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function GET() {\n  try {\n    // Test if pdfjs-dist can be imported without issues\n    const pdfjsLib = await import('pdfjs-dist');\n\n    return NextResponse.json({\n      status: 'success',\n      message: 'PDF.js library loaded successfully',\n      version: pdfjsLib.version || 'unknown'\n    });\n  } catch (error) {\n    console.error('PDF.js test error:', error);\n    return NextResponse.json(\n      {\n        status: 'error',\n        message: 'Failed to load PDF.js library',\n        error: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,IAAI;QACF,oDAAoD;QACpD,MAAM,WAAW;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,SAAS;YACT,SAAS,SAAS,OAAO,IAAI;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,QAAQ;YACR,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}