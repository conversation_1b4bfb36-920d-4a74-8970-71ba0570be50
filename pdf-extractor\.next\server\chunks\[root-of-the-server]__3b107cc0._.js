module.exports = {

"[project]/.next-internal/server/app/api/compare-pdfs/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/services/pdfComparator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PDFComparator": ()=>PDFComparator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
class PDFComparator {
    static async comparePDFs(documents) {
        try {
            if (documents.length < 2) {
                throw new Error('At least 2 documents are required for comparison');
            }
            const comparisonAnalysis = await this.performAIComparison(documents);
            return {
                id: this.generateId(),
                documents,
                differences: comparisonAnalysis.differences,
                summary: comparisonAnalysis.summary,
                recommendations: comparisonAnalysis.recommendations,
                comparedAt: new Date().toISOString(),
                comparisonType: this.detectComparisonType(documents)
            };
        } catch (error) {
            console.error('PDF comparison error:', error);
            throw error;
        }
    }
    static async performAIComparison(documents) {
        try {
            const prompt = this.createComparisonPrompt(documents);
            const completion = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert document comparison analyst specializing in business documents, quotations, contracts, and proposals. Compare the provided documents and identify key differences, focusing on pricing, terms, vendors, timelines, and other critical business factors."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 3000
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from OpenAI');
            }
            const analysis = JSON.parse(response);
            return this.validateComparisonAnalysis(analysis);
        } catch (error) {
            console.error('AI comparison error:', error);
            return this.createFallbackComparison(documents);
        }
    }
    static createComparisonPrompt(documents) {
        const documentSummaries = documents.map((doc, index)=>{
            const quotationDetails = doc.aiAnalysis?.quotationDetails;
            return `
Document ${index + 1}: ${doc.fileName}
- Document Type: ${doc.aiAnalysis?.documentType || 'unknown'}
- Content Preview: ${doc.content.fullText.substring(0, 1000)}...
- Quotation Details: ${quotationDetails ? JSON.stringify(quotationDetails, null, 2) : 'None extracted'}
- Key Insights: ${doc.aiAnalysis?.keyInsights?.join('; ') || 'None'}
`;
        }).join('\n');
        return `
Compare the following ${documents.length} business documents and provide a detailed analysis in JSON format.

${documentSummaries}

Please provide analysis in this exact JSON structure:
{
  "differences": [
    {
      "field": "pricing|vendor|terms|timeline|other",
      "type": "added|removed|changed|same",
      "oldValue": "value from first document or null",
      "newValue": "value from second document or null", 
      "significance": "high|medium|low",
      "description": "Clear description of the difference"
    }
  ],
  "summary": "Overall summary of key differences between documents",
  "recommendations": [
    "Actionable recommendations based on the comparison"
  ]
}

Focus on comparing:
1. PRICING: Total costs, line item prices, discounts, taxes
2. VENDORS: Company names, contact information, credentials
3. TERMS: Payment terms, delivery dates, warranties, conditions
4. TIMELINE: Project duration, milestones, deadlines
5. SCOPE: Services/products included, specifications, quantities
6. RISKS: Potential issues, missing information, unclear terms

For each difference, assess:
- Business impact (high/medium/low significance)
- Financial implications
- Risk factors
- Recommendations for decision making

Return only valid JSON without markdown formatting.
`;
    }
    static validateComparisonAnalysis(analysis) {
        return {
            differences: Array.isArray(analysis.differences) ? analysis.differences.map((diff)=>({
                    field: diff.field || 'other',
                    type: diff.type || 'changed',
                    oldValue: diff.oldValue,
                    newValue: diff.newValue,
                    significance: diff.significance || 'medium',
                    description: diff.description || 'No description available'
                })) : [],
            summary: analysis.summary || 'Comparison completed',
            recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : []
        };
    }
    static createFallbackComparison(documents) {
        const differences = [];
        // Basic comparison of document types
        const docTypes = documents.map((d)=>d.aiAnalysis?.documentType || 'unknown');
        if (new Set(docTypes).size > 1) {
            differences.push({
                field: 'document_type',
                type: 'changed',
                oldValue: docTypes[0],
                newValue: docTypes[1],
                significance: 'medium',
                description: `Document types differ: ${docTypes.join(' vs ')}`
            });
        }
        // Basic comparison of file sizes
        const sizes = documents.map((d)=>d.statistics.totalCharacters);
        const sizeDiff = Math.abs(sizes[0] - sizes[1]) / Math.max(sizes[0], sizes[1]);
        if (sizeDiff > 0.2) {
            differences.push({
                field: 'content_length',
                type: 'changed',
                oldValue: sizes[0],
                newValue: sizes[1],
                significance: 'low',
                description: `Significant difference in document length: ${sizeDiff > 0.5 ? 'major' : 'moderate'} variation`
            });
        }
        return {
            differences,
            summary: `Basic comparison of ${documents.length} documents completed. AI analysis unavailable.`,
            recommendations: [
                'Review documents manually for detailed comparison',
                'Check pricing and terms carefully',
                'Verify vendor information and credentials'
            ]
        };
    }
    static detectComparisonType(documents) {
        const types = documents.map((d)=>d.aiAnalysis?.documentType).filter(Boolean);
        if (types.every((type)=>type === 'quotation')) return 'quotation';
        if (types.every((type)=>type === 'contract')) return 'contract';
        if (types.every((type)=>type === 'invoice')) return 'invoice';
        return 'general';
    }
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // Generate side-by-side comparison with scoring
    static async generateSideBySideComparison(documents) {
        try {
            const scores = await this.scoreQuotations(documents);
            const comparisonMatrix = this.buildComparisonMatrix(documents);
            const bestChoice = this.determineBestChoice(scores);
            return {
                documents,
                scores,
                bestChoice,
                comparisonMatrix
            };
        } catch (error) {
            console.error('Side-by-side comparison error:', error);
            return this.createFallbackSideBySide(documents);
        }
    }
    static async scoreQuotations(documents) {
        try {
            const prompt = this.createScoringPrompt(documents);
            const completion = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert procurement analyst. Score and evaluate quotations based on pricing, terms, vendor reliability, timeline, and completeness. Provide detailed scoring and recommendations."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2500
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from OpenAI');
            }
            const analysis = JSON.parse(response);
            return this.validateQuotationScores(analysis.scores, documents);
        } catch (error) {
            console.error('Quotation scoring error:', error);
            return this.createFallbackScores(documents);
        }
    }
    static createScoringPrompt(documents) {
        const documentSummaries = documents.map((doc, index)=>{
            const quotationDetails = doc.aiAnalysis?.quotationDetails;
            return `
Document ${index + 1}: ${doc.fileName}
- Total Amount: ${quotationDetails?.totals?.total || 'Not specified'} ${quotationDetails?.totals?.currency || ''}
- Vendor: ${quotationDetails?.vendor?.name || 'Not specified'}
- Valid Until: ${quotationDetails?.validUntil || 'Not specified'}
- Items Count: ${quotationDetails?.items?.length || 0}
- Terms: ${quotationDetails?.terms?.join('; ') || 'Not specified'}
- Content Quality: ${doc.statistics.totalWords} words, ${doc.statistics.totalPages} pages
`;
        }).join('\n');
        return `
Score and evaluate the following ${documents.length} quotations for a procurement decision.

${documentSummaries}

Provide scoring in this exact JSON format:
{
  "scores": [
    {
      "documentId": "document_id",
      "fileName": "filename",
      "totalScore": 85,
      "scores": {
        "pricing": 90,
        "terms": 80,
        "vendor": 85,
        "timeline": 85,
        "completeness": 90
      },
      "strengths": ["Lower total cost", "Flexible payment terms", "Fast delivery"],
      "weaknesses": ["Unknown vendor reputation", "Limited warranty"],
      "recommendation": "best|good|acceptable|poor"
    }
  ]
}

Scoring criteria (0-100):
- PRICING: Lower cost = higher score, consider value for money
- TERMS: Better payment terms, warranties, conditions = higher score
- VENDOR: Reputation, reliability, past performance = higher score
- TIMELINE: Faster delivery, realistic schedules = higher score
- COMPLETENESS: Detailed specifications, clear terms = higher score

Calculate totalScore as weighted average: pricing(30%) + terms(25%) + vendor(20%) + timeline(15%) + completeness(10%)

Return only valid JSON without markdown formatting.
`;
    }
    // Helper method to get detailed comparison analysis
    static async getDetailedAnalysis(comparison) {
        const differences = comparison.differences;
        // Generate side-by-side comparison if quotation type
        let sideBySide;
        if (comparison.comparisonType === 'quotation' && comparison.documents.length >= 2) {
            sideBySide = await this.generateSideBySideComparison(comparison.documents);
        }
        return {
            pricing: {
                differences: differences.filter((d)=>d.field.includes('pricing') || d.field.includes('cost') || d.field.includes('total')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('pricing')))
            },
            terms: {
                differences: differences.filter((d)=>d.field.includes('terms') || d.field.includes('condition')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('terms')))
            },
            vendors: {
                differences: differences.filter((d)=>d.field.includes('vendor') || d.field.includes('supplier')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('vendor')))
            },
            timeline: {
                differences: differences.filter((d)=>d.field.includes('date') || d.field.includes('timeline') || d.field.includes('delivery')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('date')))
            },
            overall: {
                recommendation: comparison.summary,
                bestOption: this.determineBestOption(comparison),
                riskFactors: this.identifyRiskFactors(comparison)
            },
            sideBySide
        };
    }
    static summarizeDifferences(differences) {
        if (differences.length === 0) return 'No significant differences found';
        const highImpact = differences.filter((d)=>d.significance === 'high').length;
        const mediumImpact = differences.filter((d)=>d.significance === 'medium').length;
        return `Found ${differences.length} difference(s): ${highImpact} high impact, ${mediumImpact} medium impact`;
    }
    static determineBestOption(comparison) {
        // Simple logic to determine best option based on differences
        const highImpactDiffs = comparison.differences.filter((d)=>d.significance === 'high');
        if (highImpactDiffs.length === 0) {
            return 'Options are comparable - review based on your priorities';
        }
        return 'Manual review recommended due to significant differences';
    }
    static identifyRiskFactors(comparison) {
        const risks = [];
        const highImpactDiffs = comparison.differences.filter((d)=>d.significance === 'high');
        if (highImpactDiffs.length > 0) {
            risks.push(`${highImpactDiffs.length} high-impact differences require attention`);
        }
        const pricingDiffs = comparison.differences.filter((d)=>d.field.includes('pricing'));
        if (pricingDiffs.length > 0) {
            risks.push('Pricing variations detected - verify total costs');
        }
        return risks;
    }
    static validateQuotationScores(scores, documents) {
        return documents.map((doc, index)=>{
            const score = scores[index] || {};
            return {
                documentId: doc.id,
                fileName: doc.fileName,
                totalScore: Math.min(Math.max(score.totalScore || 50, 0), 100),
                scores: {
                    pricing: Math.min(Math.max(score.scores?.pricing || 50, 0), 100),
                    terms: Math.min(Math.max(score.scores?.terms || 50, 0), 100),
                    vendor: Math.min(Math.max(score.scores?.vendor || 50, 0), 100),
                    timeline: Math.min(Math.max(score.scores?.timeline || 50, 0), 100),
                    completeness: Math.min(Math.max(score.scores?.completeness || 50, 0), 100)
                },
                strengths: Array.isArray(score.strengths) ? score.strengths : [
                    'Analysis pending'
                ],
                weaknesses: Array.isArray(score.weaknesses) ? score.weaknesses : [
                    'Analysis pending'
                ],
                recommendation: [
                    'best',
                    'good',
                    'acceptable',
                    'poor'
                ].includes(score.recommendation) ? score.recommendation : 'acceptable'
            };
        });
    }
    static createFallbackScores(documents) {
        return documents.map((doc, index)=>({
                documentId: doc.id,
                fileName: doc.fileName,
                totalScore: 70 - index * 5,
                scores: {
                    pricing: 70,
                    terms: 70,
                    vendor: 70,
                    timeline: 70,
                    completeness: 70
                },
                strengths: [
                    'Document processed successfully'
                ],
                weaknesses: [
                    'AI analysis unavailable'
                ],
                recommendation: index === 0 ? 'good' : 'acceptable'
            }));
    }
    static buildComparisonMatrix(documents) {
        const matrix = [];
        // Extract key fields for comparison
        const fields = [
            {
                key: 'total_amount',
                category: 'pricing',
                label: 'Total Amount'
            },
            {
                key: 'vendor_name',
                category: 'vendor',
                label: 'Vendor Name'
            },
            {
                key: 'valid_until',
                category: 'timeline',
                label: 'Valid Until'
            },
            {
                key: 'payment_terms',
                category: 'terms',
                label: 'Payment Terms'
            },
            {
                key: 'delivery_time',
                category: 'timeline',
                label: 'Delivery Time'
            },
            {
                key: 'warranty',
                category: 'terms',
                label: 'Warranty'
            }
        ];
        fields.forEach((field)=>{
            const values = documents.map((doc)=>{
                const quotation = doc.aiAnalysis?.quotationDetails;
                let value = 'Not specified';
                switch(field.key){
                    case 'total_amount':
                        value = quotation?.totals?.total ? `${quotation.totals.total} ${quotation.totals.currency || ''}` : 'Not specified';
                        break;
                    case 'vendor_name':
                        value = quotation?.vendor?.name || 'Not specified';
                        break;
                    case 'valid_until':
                        value = quotation?.validUntil || 'Not specified';
                        break;
                    case 'payment_terms':
                        value = quotation?.terms?.find((t)=>t.toLowerCase().includes('payment')) || 'Not specified';
                        break;
                    case 'delivery_time':
                        value = quotation?.terms?.find((t)=>t.toLowerCase().includes('delivery')) || 'Not specified';
                        break;
                    case 'warranty':
                        value = quotation?.terms?.find((t)=>t.toLowerCase().includes('warranty')) || 'Not specified';
                        break;
                }
                return {
                    documentId: doc.id,
                    value,
                    isBest: false,
                    isWorst: false,
                    score: 70 // Default score
                };
            });
            // Determine best/worst for this field
            if (field.category === 'pricing') {
                // For pricing, lower is better
                const amounts = values.map((v)=>{
                    const match = v.value.match(/[\d,]+\.?\d*/);
                    return match ? parseFloat(match[0].replace(/,/g, '')) : Infinity;
                });
                const minAmount = Math.min(...amounts);
                const maxAmount = Math.max(...amounts);
                values.forEach((v, i)=>{
                    if (amounts[i] === minAmount && minAmount !== Infinity) v.isBest = true;
                    if (amounts[i] === maxAmount && maxAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;
                    v.score = amounts[i] === Infinity ? 50 : Math.round(100 - (amounts[i] - minAmount) / (maxAmount - minAmount) * 50);
                });
            } else {
                // For other fields, mark first non-empty as best
                const firstValid = values.find((v)=>v.value !== 'Not specified');
                if (firstValid) firstValid.isBest = true;
                values.forEach((v)=>{
                    v.score = v.value !== 'Not specified' ? 80 : 30;
                });
            }
            matrix.push({
                field: field.label,
                category: field.category,
                values
            });
        });
        return matrix;
    }
    static determineBestChoice(scores) {
        const bestScore = scores.reduce((best, current)=>current.totalScore > best.totalScore ? current : best);
        return {
            documentId: bestScore.documentId,
            reason: `Highest overall score with strong performance in ${bestScore.strengths.slice(0, 2).join(' and ')}`,
            confidence: Math.min(bestScore.totalScore / 100, 0.95)
        };
    }
    static createFallbackSideBySide(documents) {
        const scores = this.createFallbackScores(documents);
        const comparisonMatrix = this.buildComparisonMatrix(documents);
        const bestChoice = this.determineBestChoice(scores);
        return {
            documents,
            scores,
            bestChoice,
            comparisonMatrix
        };
    }
}
}),
"[project]/src/app/api/compare-pdfs/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$pdfComparator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/pdfComparator.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { documents } = body;
        if (!documents || !Array.isArray(documents) || documents.length < 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'At least 2 documents are required for comparison'
            }, {
                status: 400
            });
        }
        if (documents.length > 5) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Maximum 5 documents can be compared at once'
            }, {
                status: 400
            });
        }
        // Validate document structure
        for (const doc of documents){
            if (!doc.id || !doc.fileName || !doc.content) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid document structure'
                }, {
                    status: 400
                });
            }
        }
        const comparison = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$pdfComparator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PDFComparator"].comparePDFs(documents);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(comparison);
    } catch (error) {
        console.error('PDF comparison error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to compare PDFs. Please try again.'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3b107cc0._.js.map