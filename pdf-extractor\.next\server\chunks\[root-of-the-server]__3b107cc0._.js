module.exports = {

"[project]/.next-internal/server/app/api/compare-pdfs/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/services/pdfComparator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "PDFComparator": ()=>PDFComparator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
class PDFComparator {
    static async comparePDFs(documents) {
        try {
            if (documents.length < 2) {
                throw new Error('At least 2 documents are required for comparison');
            }
            const comparisonAnalysis = await this.performAIComparison(documents);
            return {
                id: this.generateId(),
                documents,
                differences: comparisonAnalysis.differences,
                summary: comparisonAnalysis.summary,
                recommendations: comparisonAnalysis.recommendations,
                comparedAt: new Date().toISOString(),
                comparisonType: this.detectComparisonType(documents)
            };
        } catch (error) {
            console.error('PDF comparison error:', error);
            throw error;
        }
    }
    static async performAIComparison(documents) {
        try {
            const prompt = this.createComparisonPrompt(documents);
            const completion = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert document comparison analyst specializing in business documents, quotations, contracts, and proposals. Compare the provided documents and identify key differences, focusing on pricing, terms, vendors, timelines, and other critical business factors."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 3000
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from OpenAI');
            }
            const analysis = JSON.parse(response);
            return this.validateComparisonAnalysis(analysis);
        } catch (error) {
            console.error('AI comparison error:', error);
            return this.createFallbackComparison(documents);
        }
    }
    static createComparisonPrompt(documents) {
        const documentSummaries = documents.map((doc, index)=>{
            const quotationDetails = doc.aiAnalysis?.quotationDetails;
            return `
Document ${index + 1}: ${doc.fileName}
- Document Type: ${doc.aiAnalysis?.documentType || 'unknown'}
- Content Preview: ${doc.content.fullText.substring(0, 1000)}...
- Quotation Details: ${quotationDetails ? JSON.stringify(quotationDetails, null, 2) : 'None extracted'}
- Key Insights: ${doc.aiAnalysis?.keyInsights?.join('; ') || 'None'}
`;
        }).join('\n');
        return `
Compare the following ${documents.length} business documents and provide a detailed analysis in JSON format.

${documentSummaries}

Please provide analysis in this exact JSON structure:
{
  "differences": [
    {
      "field": "pricing|vendor|terms|timeline|other",
      "type": "added|removed|changed|same",
      "oldValue": "value from first document or null",
      "newValue": "value from second document or null", 
      "significance": "high|medium|low",
      "description": "Clear description of the difference"
    }
  ],
  "summary": "Overall summary of key differences between documents",
  "recommendations": [
    "Actionable recommendations based on the comparison"
  ]
}

Focus on comparing:
1. PRICING: Total costs, line item prices, discounts, taxes
2. VENDORS: Company names, contact information, credentials
3. TERMS: Payment terms, delivery dates, warranties, conditions
4. TIMELINE: Project duration, milestones, deadlines
5. SCOPE: Services/products included, specifications, quantities
6. RISKS: Potential issues, missing information, unclear terms

For each difference, assess:
- Business impact (high/medium/low significance)
- Financial implications
- Risk factors
- Recommendations for decision making

Return only valid JSON without markdown formatting.
`;
    }
    static validateComparisonAnalysis(analysis) {
        return {
            differences: Array.isArray(analysis.differences) ? analysis.differences.map((diff)=>({
                    field: diff.field || 'other',
                    type: diff.type || 'changed',
                    oldValue: diff.oldValue,
                    newValue: diff.newValue,
                    significance: diff.significance || 'medium',
                    description: diff.description || 'No description available'
                })) : [],
            summary: analysis.summary || 'Comparison completed',
            recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : []
        };
    }
    static createFallbackComparison(documents) {
        const differences = [];
        // Basic comparison of document types
        const docTypes = documents.map((d)=>d.aiAnalysis?.documentType || 'unknown');
        if (new Set(docTypes).size > 1) {
            differences.push({
                field: 'document_type',
                type: 'changed',
                oldValue: docTypes[0],
                newValue: docTypes[1],
                significance: 'medium',
                description: `Document types differ: ${docTypes.join(' vs ')}`
            });
        }
        // Basic comparison of file sizes
        const sizes = documents.map((d)=>d.statistics.totalCharacters);
        const sizeDiff = Math.abs(sizes[0] - sizes[1]) / Math.max(sizes[0], sizes[1]);
        if (sizeDiff > 0.2) {
            differences.push({
                field: 'content_length',
                type: 'changed',
                oldValue: sizes[0],
                newValue: sizes[1],
                significance: 'low',
                description: `Significant difference in document length: ${sizeDiff > 0.5 ? 'major' : 'moderate'} variation`
            });
        }
        return {
            differences,
            summary: `Basic comparison of ${documents.length} documents completed. AI analysis unavailable.`,
            recommendations: [
                'Review documents manually for detailed comparison',
                'Check pricing and terms carefully',
                'Verify vendor information and credentials'
            ]
        };
    }
    static detectComparisonType(documents) {
        const types = documents.map((d)=>d.aiAnalysis?.documentType).filter(Boolean);
        if (types.every((type)=>type === 'quotation')) return 'quotation';
        if (types.every((type)=>type === 'contract')) return 'contract';
        if (types.every((type)=>type === 'invoice')) return 'invoice';
        return 'general';
    }
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    // Helper method to get detailed comparison analysis
    static async getDetailedAnalysis(comparison) {
        const differences = comparison.differences;
        return {
            pricing: {
                differences: differences.filter((d)=>d.field.includes('pricing') || d.field.includes('cost') || d.field.includes('total')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('pricing')))
            },
            terms: {
                differences: differences.filter((d)=>d.field.includes('terms') || d.field.includes('condition')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('terms')))
            },
            vendors: {
                differences: differences.filter((d)=>d.field.includes('vendor') || d.field.includes('supplier')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('vendor')))
            },
            timeline: {
                differences: differences.filter((d)=>d.field.includes('date') || d.field.includes('timeline') || d.field.includes('delivery')),
                summary: this.summarizeDifferences(differences.filter((d)=>d.field.includes('date')))
            },
            overall: {
                recommendation: comparison.summary,
                bestOption: this.determineBestOption(comparison),
                riskFactors: this.identifyRiskFactors(comparison)
            }
        };
    }
    static summarizeDifferences(differences) {
        if (differences.length === 0) return 'No significant differences found';
        const highImpact = differences.filter((d)=>d.significance === 'high').length;
        const mediumImpact = differences.filter((d)=>d.significance === 'medium').length;
        return `Found ${differences.length} difference(s): ${highImpact} high impact, ${mediumImpact} medium impact`;
    }
    static determineBestOption(comparison) {
        // Simple logic to determine best option based on differences
        const highImpactDiffs = comparison.differences.filter((d)=>d.significance === 'high');
        if (highImpactDiffs.length === 0) {
            return 'Options are comparable - review based on your priorities';
        }
        return 'Manual review recommended due to significant differences';
    }
    static identifyRiskFactors(comparison) {
        const risks = [];
        const highImpactDiffs = comparison.differences.filter((d)=>d.significance === 'high');
        if (highImpactDiffs.length > 0) {
            risks.push(`${highImpactDiffs.length} high-impact differences require attention`);
        }
        const pricingDiffs = comparison.differences.filter((d)=>d.field.includes('pricing'));
        if (pricingDiffs.length > 0) {
            risks.push('Pricing variations detected - verify total costs');
        }
        return risks;
    }
}
}),
"[project]/src/app/api/compare-pdfs/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$pdfComparator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/pdfComparator.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const body = await request.json();
        const { documents } = body;
        if (!documents || !Array.isArray(documents) || documents.length < 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'At least 2 documents are required for comparison'
            }, {
                status: 400
            });
        }
        if (documents.length > 5) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Maximum 5 documents can be compared at once'
            }, {
                status: 400
            });
        }
        // Validate document structure
        for (const doc of documents){
            if (!doc.id || !doc.fileName || !doc.content) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid document structure'
                }, {
                    status: 400
                });
            }
        }
        const comparison = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$pdfComparator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PDFComparator"].comparePDFs(documents);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(comparison);
    } catch (error) {
        console.error('PDF comparison error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to compare PDFs. Please try again.'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3b107cc0._.js.map