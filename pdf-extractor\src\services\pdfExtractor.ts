import { ExtractedPDFData } from '@/types/pdf';

export class PDFExtractorService {
  static async extractPDFData(file: File): Promise<ExtractedPDFData> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/extract-pdf', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to extract PDF data');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('PDF extraction error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to extract PDF data. Please ensure the file is a valid PDF.');
    }
  }

}
