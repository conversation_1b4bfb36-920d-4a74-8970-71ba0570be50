{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/pdfComparator.ts"], "sourcesContent": ["import OpenAI from 'openai';\nimport { ExtractedPDFData, PDFComparison, ComparisonDifference, ComparisonAnalysis, SideBySideComparison, QuotationScore } from '@/types/pdf';\n\nconst openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\nexport class PDFComparator {\n  static async comparePDFs(documents: ExtractedPDFData[]): Promise<PDFComparison> {\n    try {\n      if (documents.length < 2) {\n        throw new Error('At least 2 documents are required for comparison');\n      }\n\n      const comparisonAnalysis = await this.performAIComparison(documents);\n\n      return {\n        id: this.generateId(),\n        documents,\n        differences: comparisonAnalysis.differences,\n        summary: comparisonAnalysis.summary,\n        recommendations: comparisonAnalysis.recommendations,\n        comparedAt: new Date().toISOString(),\n        comparisonType: this.detectComparisonType(documents)\n      };\n    } catch (error) {\n      console.error('PDF comparison error:', error);\n      throw error;\n    }\n  }\n\n  private static async performAIComparison(documents: ExtractedPDFData[]): Promise<{\n    differences: ComparisonDifference[];\n    summary: string;\n    recommendations: string[];\n  }> {\n    try {\n      const prompt = this.createComparisonPrompt(documents);\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4o-mini\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are an expert document comparison analyst specializing in business documents, quotations, contracts, and proposals. Compare the provided documents and identify key differences, focusing on pricing, terms, vendors, timelines, and other critical business factors.\"\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        temperature: 0.1,\n        max_tokens: 3000,\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('No response from OpenAI');\n      }\n\n      const analysis = JSON.parse(response);\n      return this.validateComparisonAnalysis(analysis);\n    } catch (error) {\n      console.error('AI comparison error:', error);\n      return this.createFallbackComparison(documents);\n    }\n  }\n\n  private static createComparisonPrompt(documents: ExtractedPDFData[]): string {\n    const documentSummaries = documents.map((doc, index) => {\n      const quotationDetails = doc.aiAnalysis?.quotationDetails;\n      return `\nDocument ${index + 1}: ${doc.fileName}\n- Document Type: ${doc.aiAnalysis?.documentType || 'unknown'}\n- Content Preview: ${doc.content.fullText.substring(0, 1000)}...\n- Quotation Details: ${quotationDetails ? JSON.stringify(quotationDetails, null, 2) : 'None extracted'}\n- Key Insights: ${doc.aiAnalysis?.keyInsights?.join('; ') || 'None'}\n`;\n    }).join('\\n');\n\n    return `\nCompare the following ${documents.length} business documents and provide a detailed analysis in JSON format.\n\n${documentSummaries}\n\nPlease provide analysis in this exact JSON structure:\n{\n  \"differences\": [\n    {\n      \"field\": \"pricing|vendor|terms|timeline|other\",\n      \"type\": \"added|removed|changed|same\",\n      \"oldValue\": \"value from first document or null\",\n      \"newValue\": \"value from second document or null\", \n      \"significance\": \"high|medium|low\",\n      \"description\": \"Clear description of the difference\"\n    }\n  ],\n  \"summary\": \"Overall summary of key differences between documents\",\n  \"recommendations\": [\n    \"Actionable recommendations based on the comparison\"\n  ]\n}\n\nFocus on comparing:\n1. PRICING: Total costs, line item prices, discounts, taxes\n2. VENDORS: Company names, contact information, credentials\n3. TERMS: Payment terms, delivery dates, warranties, conditions\n4. TIMELINE: Project duration, milestones, deadlines\n5. SCOPE: Services/products included, specifications, quantities\n6. RISKS: Potential issues, missing information, unclear terms\n\nFor each difference, assess:\n- Business impact (high/medium/low significance)\n- Financial implications\n- Risk factors\n- Recommendations for decision making\n\nReturn only valid JSON without markdown formatting.\n`;\n  }\n\n  private static validateComparisonAnalysis(analysis: any): {\n    differences: ComparisonDifference[];\n    summary: string;\n    recommendations: string[];\n  } {\n    return {\n      differences: Array.isArray(analysis.differences)\n        ? analysis.differences.map((diff: any) => ({\n          field: diff.field || 'other',\n          type: diff.type || 'changed',\n          oldValue: diff.oldValue,\n          newValue: diff.newValue,\n          significance: diff.significance || 'medium',\n          description: diff.description || 'No description available'\n        }))\n        : [],\n      summary: analysis.summary || 'Comparison completed',\n      recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : []\n    };\n  }\n\n  private static createFallbackComparison(documents: ExtractedPDFData[]): {\n    differences: ComparisonDifference[];\n    summary: string;\n    recommendations: string[];\n  } {\n    const differences: ComparisonDifference[] = [];\n\n    // Basic comparison of document types\n    const docTypes = documents.map(d => d.aiAnalysis?.documentType || 'unknown');\n    if (new Set(docTypes).size > 1) {\n      differences.push({\n        field: 'document_type',\n        type: 'changed',\n        oldValue: docTypes[0],\n        newValue: docTypes[1],\n        significance: 'medium',\n        description: `Document types differ: ${docTypes.join(' vs ')}`\n      });\n    }\n\n    // Basic comparison of file sizes\n    const sizes = documents.map(d => d.statistics.totalCharacters);\n    const sizeDiff = Math.abs(sizes[0] - sizes[1]) / Math.max(sizes[0], sizes[1]);\n    if (sizeDiff > 0.2) {\n      differences.push({\n        field: 'content_length',\n        type: 'changed',\n        oldValue: sizes[0],\n        newValue: sizes[1],\n        significance: 'low',\n        description: `Significant difference in document length: ${sizeDiff > 0.5 ? 'major' : 'moderate'} variation`\n      });\n    }\n\n    return {\n      differences,\n      summary: `Basic comparison of ${documents.length} documents completed. AI analysis unavailable.`,\n      recommendations: [\n        'Review documents manually for detailed comparison',\n        'Check pricing and terms carefully',\n        'Verify vendor information and credentials'\n      ]\n    };\n  }\n\n  private static detectComparisonType(documents: ExtractedPDFData[]): 'quotation' | 'contract' | 'invoice' | 'general' {\n    const types = documents.map(d => d.aiAnalysis?.documentType).filter(Boolean);\n\n    if (types.every(type => type === 'quotation')) return 'quotation';\n    if (types.every(type => type === 'contract')) return 'contract';\n    if (types.every(type => type === 'invoice')) return 'invoice';\n\n    return 'general';\n  }\n\n  private static generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  // Generate side-by-side comparison with scoring\n  static async generateSideBySideComparison(documents: ExtractedPDFData[]): Promise<SideBySideComparison> {\n    try {\n      const scores = await this.scoreQuotations(documents);\n      const comparisonMatrix = this.buildComparisonMatrix(documents);\n      const bestChoice = this.determineBestChoice(scores);\n\n      return {\n        documents,\n        scores,\n        bestChoice,\n        comparisonMatrix\n      };\n    } catch (error) {\n      console.error('Side-by-side comparison error:', error);\n      return this.createFallbackSideBySide(documents);\n    }\n  }\n\n  private static async scoreQuotations(documents: ExtractedPDFData[]): Promise<QuotationScore[]> {\n    try {\n      const prompt = this.createScoringPrompt(documents);\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4o-mini\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are an expert procurement analyst. Score and evaluate quotations based on pricing, terms, vendor reliability, timeline, and completeness. Provide detailed scoring and recommendations.\"\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        temperature: 0.1,\n        max_tokens: 2500,\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('No response from OpenAI');\n      }\n\n      const analysis = JSON.parse(response);\n      return this.validateQuotationScores(analysis.scores, documents);\n    } catch (error) {\n      console.error('Quotation scoring error:', error);\n      return this.createFallbackScores(documents);\n    }\n  }\n\n  private static createScoringPrompt(documents: ExtractedPDFData[]): string {\n    const documentSummaries = documents.map((doc, index) => {\n      const quotationDetails = doc.aiAnalysis?.quotationDetails;\n      return `\nDocument ${index + 1}: ${doc.fileName}\n- Total Amount: ${quotationDetails?.totals?.total || 'Not specified'} ${quotationDetails?.totals?.currency || ''}\n- Vendor: ${quotationDetails?.vendor?.name || 'Not specified'}\n- Valid Until: ${quotationDetails?.validUntil || 'Not specified'}\n- Items Count: ${quotationDetails?.items?.length || 0}\n- Terms: ${quotationDetails?.terms?.join('; ') || 'Not specified'}\n- Content Quality: ${doc.statistics.totalWords} words, ${doc.statistics.totalPages} pages\n`;\n    }).join('\\n');\n\n    return `\nScore and evaluate the following ${documents.length} quotations for a procurement decision.\n\n${documentSummaries}\n\nProvide scoring in this exact JSON format:\n{\n  \"scores\": [\n    {\n      \"documentId\": \"document_id\",\n      \"fileName\": \"filename\",\n      \"totalScore\": 85,\n      \"scores\": {\n        \"pricing\": 90,\n        \"terms\": 80,\n        \"vendor\": 85,\n        \"timeline\": 85,\n        \"completeness\": 90\n      },\n      \"strengths\": [\"Lower total cost\", \"Flexible payment terms\", \"Fast delivery\"],\n      \"weaknesses\": [\"Unknown vendor reputation\", \"Limited warranty\"],\n      \"recommendation\": \"best|good|acceptable|poor\"\n    }\n  ]\n}\n\nScoring criteria (0-100):\n- PRICING: Lower premium + higher liability limits + lower deductible = higher score\n- TERMS: Better policy terms, broader territory, favorable jurisdiction = higher score\n- VENDOR: Insurer reputation, financial strength, claims handling = higher score\n- TIMELINE: Policy period, retroactive coverage = higher score\n- COMPLETENESS: Comprehensive coverage, fewer exclusions = higher score\n\nCalculate totalScore as weighted average: pricing(35%) + terms(25%) + vendor(20%) + timeline(10%) + completeness(10%)\n\nFocus on insurance-specific factors:\n- Premium competitiveness vs coverage provided\n- Liability limits adequacy\n- Deductible levels\n- Coverage extensions included\n- Major exclusions present\n- Policy territory and jurisdiction\n- Insurer financial strength\n\nReturn only valid JSON without markdown formatting.\n`;\n  }\n\n  // Helper method to get detailed comparison analysis\n  static async getDetailedAnalysis(comparison: PDFComparison): Promise<ComparisonAnalysis> {\n    const differences = comparison.differences;\n\n    // Generate side-by-side comparison if quotation type\n    let sideBySide: SideBySideComparison | undefined;\n    if (comparison.comparisonType === 'quotation' && comparison.documents.length >= 2) {\n      sideBySide = await this.generateSideBySideComparison(comparison.documents);\n    }\n\n    return {\n      pricing: {\n        differences: differences.filter(d => d.field.includes('pricing') || d.field.includes('cost') || d.field.includes('total')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('pricing')))\n      },\n      terms: {\n        differences: differences.filter(d => d.field.includes('terms') || d.field.includes('condition')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('terms')))\n      },\n      vendors: {\n        differences: differences.filter(d => d.field.includes('vendor') || d.field.includes('supplier')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('vendor')))\n      },\n      timeline: {\n        differences: differences.filter(d => d.field.includes('date') || d.field.includes('timeline') || d.field.includes('delivery')),\n        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('date')))\n      },\n      overall: {\n        recommendation: comparison.summary,\n        bestOption: this.determineBestOption(comparison),\n        riskFactors: this.identifyRiskFactors(comparison)\n      },\n      sideBySide\n    };\n  }\n\n  private static summarizeDifferences(differences: ComparisonDifference[]): string {\n    if (differences.length === 0) return 'No significant differences found';\n\n    const highImpact = differences.filter(d => d.significance === 'high').length;\n    const mediumImpact = differences.filter(d => d.significance === 'medium').length;\n\n    return `Found ${differences.length} difference(s): ${highImpact} high impact, ${mediumImpact} medium impact`;\n  }\n\n  private static determineBestOption(comparison: PDFComparison): string | undefined {\n    // Simple logic to determine best option based on differences\n    const highImpactDiffs = comparison.differences.filter(d => d.significance === 'high');\n    if (highImpactDiffs.length === 0) {\n      return 'Options are comparable - review based on your priorities';\n    }\n\n    return 'Manual review recommended due to significant differences';\n  }\n\n  private static identifyRiskFactors(comparison: PDFComparison): string[] {\n    const risks: string[] = [];\n\n    const highImpactDiffs = comparison.differences.filter(d => d.significance === 'high');\n    if (highImpactDiffs.length > 0) {\n      risks.push(`${highImpactDiffs.length} high-impact differences require attention`);\n    }\n\n    const pricingDiffs = comparison.differences.filter(d => d.field.includes('pricing'));\n    if (pricingDiffs.length > 0) {\n      risks.push('Pricing variations detected - verify total costs');\n    }\n\n    return risks;\n  }\n\n  private static validateQuotationScores(scores: any[], documents: ExtractedPDFData[]): QuotationScore[] {\n    return documents.map((doc, index) => {\n      const score = scores[index] || {};\n      return {\n        documentId: doc.id,\n        fileName: doc.fileName,\n        totalScore: Math.min(Math.max(score.totalScore || 50, 0), 100),\n        scores: {\n          pricing: Math.min(Math.max(score.scores?.pricing || 50, 0), 100),\n          terms: Math.min(Math.max(score.scores?.terms || 50, 0), 100),\n          vendor: Math.min(Math.max(score.scores?.vendor || 50, 0), 100),\n          timeline: Math.min(Math.max(score.scores?.timeline || 50, 0), 100),\n          completeness: Math.min(Math.max(score.scores?.completeness || 50, 0), 100)\n        },\n        strengths: Array.isArray(score.strengths) ? score.strengths : ['Analysis pending'],\n        weaknesses: Array.isArray(score.weaknesses) ? score.weaknesses : ['Analysis pending'],\n        recommendation: ['best', 'good', 'acceptable', 'poor'].includes(score.recommendation)\n          ? score.recommendation : 'acceptable'\n      };\n    });\n  }\n\n  private static createFallbackScores(documents: ExtractedPDFData[]): QuotationScore[] {\n    return documents.map((doc, index) => ({\n      documentId: doc.id,\n      fileName: doc.fileName,\n      totalScore: 70 - (index * 5), // Simple ranking\n      scores: {\n        pricing: 70,\n        terms: 70,\n        vendor: 70,\n        timeline: 70,\n        completeness: 70\n      },\n      strengths: ['Document processed successfully'],\n      weaknesses: ['AI analysis unavailable'],\n      recommendation: index === 0 ? 'good' : 'acceptable' as any\n    }));\n  }\n\n  private static buildComparisonMatrix(documents: ExtractedPDFData[]) {\n    const matrix: any[] = [];\n\n    // Insurance policy specific fields for comparison\n    const fields = [\n      // Core Policy Terms\n      { key: 'limit_of_liability', category: 'pricing', label: 'Limit of Liability' },\n      { key: 'annual_premium', category: 'pricing', label: 'Annual Premium (GST 18% additional)' },\n      { key: 'deductible', category: 'pricing', label: 'Deductible (each and every claim)' },\n      { key: 'retroactive_date', category: 'terms', label: 'Retroactive Date' },\n      { key: 'territory', category: 'terms', label: 'Territory' },\n      { key: 'jurisdiction', category: 'terms', label: 'Jurisdiction' },\n      { key: 'wording', category: 'terms', label: 'Wording' },\n\n      // Broad Coverage Extensions\n      { key: 'fake_president_fraud', category: 'coverage', label: 'Fake President Fraud Extension / Social Engineering Fraud/ Impersonation' },\n      { key: 'additional_cost_cover', category: 'coverage', label: 'Additional Cost Cover' },\n      { key: 'property_damage_robbery', category: 'coverage', label: 'Loss or Damage to property/premises by Robbery/ Criminal Damage' },\n      { key: 'safe_burglary', category: 'coverage', label: 'Loss or damage to property within safe from safe burglary/ Violent and forcible' },\n      { key: 'computer_fraud', category: 'coverage', label: 'Computer Fraud cover' },\n      { key: 'loss_third_party', category: 'coverage', label: 'Loss to Third Party' },\n      { key: 'contractual_penalties', category: 'coverage', label: 'Contractual Penalties/ Expectation Damages' },\n      { key: 'credit_card_fraud', category: 'coverage', label: 'Credit Card Fraud Cover' },\n      { key: 'funds_transfer_fraud', category: 'coverage', label: 'Funds Transfer Fraud coverage' },\n      { key: 'counterfeit_currency', category: 'coverage', label: 'Counterfeit Currency Fraud Cover' },\n      { key: 'money_securities', category: 'coverage', label: 'Money and securities - Damage, destruction and disappearance cover/ Premises Coverage' },\n      { key: 'legal_expenses', category: 'coverage', label: 'Legal Expenses / Fees, costs and expenses' },\n      { key: 'investigative_costs', category: 'coverage', label: 'Investigative costs / Investigation Specialist costs' },\n      { key: 'reconstitution_costs', category: 'coverage', label: 'Reconstitution/ Restoration Costs' },\n      { key: 'care_custody_control', category: 'coverage', label: 'Care, Custody or Control' },\n      { key: 'auto_acquisition', category: 'coverage', label: 'Auto acquisition of subsidiaries' },\n      { key: 'interest_receivable', category: 'coverage', label: 'Interest receivable or Payable' },\n      { key: 'court_attendance', category: 'coverage', label: 'Court Attendance costs' },\n      { key: 'audit_fee', category: 'coverage', label: 'Audit Fee' },\n      { key: 'control_group_clause', category: 'coverage', label: 'Control Group Clause' },\n      { key: 'other_coverages', category: 'coverage', label: 'Other Coverages' },\n\n      // Major Exclusions\n      { key: 'known_acts_claims', category: 'exclusions', label: 'Known Acts/ Claims' },\n      { key: 'consequential_loss', category: 'exclusions', label: 'Consequential Loss' },\n      { key: 'fines_penalties', category: 'exclusions', label: 'Fines & penalties' }\n    ];\n\n    fields.forEach(field => {\n      const values = documents.map(doc => {\n        const fullText = doc.content.fullText.toLowerCase();\n        let value = 'Not specified';\n\n        // Extract insurance-specific fields using text analysis\n        switch (field.key) {\n          case 'limit_of_liability':\n            value = this.extractInsuranceField(fullText, ['limit of liability', 'liability limit', 'aggregate limit']) || 'Not specified';\n            break;\n          case 'annual_premium':\n            value = this.extractInsuranceField(fullText, ['annual premium', 'premium', 'total premium']) || 'Not specified';\n            break;\n          case 'deductible':\n            value = this.extractInsuranceField(fullText, ['deductible', 'excess', 'retention']) || 'Not specified';\n            break;\n          case 'retroactive_date':\n            value = this.extractInsuranceField(fullText, ['retroactive date', 'retroactive']) || 'Not specified';\n            break;\n          case 'territory':\n            value = this.extractInsuranceField(fullText, ['territory', 'geographical limit', 'coverage area']) || 'Not specified';\n            break;\n          case 'jurisdiction':\n            value = this.extractInsuranceField(fullText, ['jurisdiction', 'governing law', 'legal jurisdiction']) || 'Not specified';\n            break;\n          case 'wording':\n            value = this.extractInsuranceField(fullText, ['wording', 'policy wording', 'terms and conditions']) || 'Not specified';\n            break;\n\n          // Coverage Extensions\n          case 'fake_president_fraud':\n            value = this.checkCoverageIncluded(fullText, ['fake president', 'social engineering', 'impersonation fraud']);\n            break;\n          case 'additional_cost_cover':\n            value = this.checkCoverageIncluded(fullText, ['additional cost', 'extra expense']);\n            break;\n          case 'property_damage_robbery':\n            value = this.checkCoverageIncluded(fullText, ['robbery', 'criminal damage', 'property damage']);\n            break;\n          case 'safe_burglary':\n            value = this.checkCoverageIncluded(fullText, ['safe burglary', 'violent and forcible']);\n            break;\n          case 'computer_fraud':\n            value = this.checkCoverageIncluded(fullText, ['computer fraud', 'cyber fraud']);\n            break;\n          case 'loss_third_party':\n            value = this.checkCoverageIncluded(fullText, ['third party', 'loss to third party']);\n            break;\n          case 'contractual_penalties':\n            value = this.checkCoverageIncluded(fullText, ['contractual penalties', 'expectation damages']);\n            break;\n          case 'credit_card_fraud':\n            value = this.checkCoverageIncluded(fullText, ['credit card fraud', 'card fraud']);\n            break;\n          case 'funds_transfer_fraud':\n            value = this.checkCoverageIncluded(fullText, ['funds transfer fraud', 'wire fraud']);\n            break;\n          case 'counterfeit_currency':\n            value = this.checkCoverageIncluded(fullText, ['counterfeit currency', 'fake currency']);\n            break;\n          case 'money_securities':\n            value = this.checkCoverageIncluded(fullText, ['money and securities', 'premises coverage']);\n            break;\n          case 'legal_expenses':\n            value = this.checkCoverageIncluded(fullText, ['legal expenses', 'legal fees']);\n            break;\n          case 'investigative_costs':\n            value = this.checkCoverageIncluded(fullText, ['investigative costs', 'investigation specialist']);\n            break;\n          case 'reconstitution_costs':\n            value = this.checkCoverageIncluded(fullText, ['reconstitution', 'restoration costs']);\n            break;\n          case 'care_custody_control':\n            value = this.checkCoverageIncluded(fullText, ['care custody control', 'care, custody or control']);\n            break;\n          case 'auto_acquisition':\n            value = this.checkCoverageIncluded(fullText, ['auto acquisition', 'automatic acquisition']);\n            break;\n          case 'interest_receivable':\n            value = this.checkCoverageIncluded(fullText, ['interest receivable', 'interest payable']);\n            break;\n          case 'court_attendance':\n            value = this.checkCoverageIncluded(fullText, ['court attendance', 'court costs']);\n            break;\n          case 'audit_fee':\n            value = this.checkCoverageIncluded(fullText, ['audit fee', 'audit costs']);\n            break;\n          case 'control_group_clause':\n            value = this.checkCoverageIncluded(fullText, ['control group', 'control group clause']);\n            break;\n          case 'other_coverages':\n            value = this.checkCoverageIncluded(fullText, ['other coverages', 'additional coverages']);\n            break;\n\n          // Exclusions\n          case 'known_acts_claims':\n            value = this.checkExclusionPresent(fullText, ['known acts', 'known claims']);\n            break;\n          case 'consequential_loss':\n            value = this.checkExclusionPresent(fullText, ['consequential loss', 'indirect loss']);\n            break;\n          case 'fines_penalties':\n            value = this.checkExclusionPresent(fullText, ['fines', 'penalties', 'fines and penalties']);\n            break;\n        }\n\n        return {\n          documentId: doc.id,\n          value,\n          isBest: false,\n          isWorst: false,\n          score: 70 // Default score\n        };\n      });\n\n      // Determine best/worst for this field based on category\n      if (field.category === 'pricing') {\n        // For pricing fields, extract and compare numerical values\n        const amounts = values.map(v => {\n          const match = v.value.match(/[\\d,]+\\.?\\d*/);\n          return match ? parseFloat(match[0].replace(/,/g, '')) : Infinity;\n        });\n        const minAmount = Math.min(...amounts);\n        const maxAmount = Math.max(...amounts);\n\n        values.forEach((v, i) => {\n          if (field.key === 'deductible') {\n            // For deductible, lower is better\n            if (amounts[i] === minAmount && minAmount !== Infinity) v.isBest = true;\n            if (amounts[i] === maxAmount && maxAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;\n          } else if (field.key === 'limit_of_liability') {\n            // For liability limit, higher is better\n            if (amounts[i] === maxAmount && maxAmount !== Infinity) v.isBest = true;\n            if (amounts[i] === minAmount && minAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;\n          } else {\n            // For premium, lower is better\n            if (amounts[i] === minAmount && minAmount !== Infinity) v.isBest = true;\n            if (amounts[i] === maxAmount && maxAmount !== Infinity && minAmount !== maxAmount) v.isWorst = true;\n          }\n          v.score = amounts[i] === Infinity ? 50 : Math.round(100 - ((amounts[i] - minAmount) / (maxAmount - minAmount)) * 50);\n        });\n      } else if (field.category === 'coverage') {\n        // For coverage, \"Included\" is better than \"Not specified\"\n        values.forEach(v => {\n          if (v.value === 'Included') {\n            v.isBest = true;\n            v.score = 100;\n          } else if (v.value === 'Excluded') {\n            v.isWorst = true;\n            v.score = 20;\n          } else {\n            v.score = 50;\n          }\n        });\n      } else if (field.category === 'exclusions') {\n        // For exclusions, \"Not present\" is better than \"Present\"\n        values.forEach(v => {\n          if (v.value === 'Not present') {\n            v.isBest = true;\n            v.score = 100;\n          } else if (v.value === 'Present') {\n            v.isWorst = true;\n            v.score = 20;\n          } else {\n            v.score = 50;\n          }\n        });\n      } else {\n        // For terms, mark first non-empty as best\n        const firstValid = values.find(v => v.value !== 'Not specified');\n        if (firstValid) firstValid.isBest = true;\n\n        values.forEach(v => {\n          v.score = v.value !== 'Not specified' ? 80 : 30;\n        });\n      }\n\n      matrix.push({\n        field: field.label,\n        category: field.category,\n        values\n      });\n    });\n\n    return matrix;\n  }\n\n  private static determineBestChoice(scores: QuotationScore[]) {\n    const bestScore = scores.reduce((best, current) =>\n      current.totalScore > best.totalScore ? current : best\n    );\n\n    return {\n      documentId: bestScore.documentId,\n      reason: `Highest overall score with strong performance in ${bestScore.strengths.slice(0, 2).join(' and ')}`,\n      confidence: Math.min(bestScore.totalScore / 100, 0.95)\n    };\n  }\n\n  private static createFallbackSideBySide(documents: ExtractedPDFData[]): SideBySideComparison {\n    const scores = this.createFallbackScores(documents);\n    const comparisonMatrix = this.buildComparisonMatrix(documents);\n    const bestChoice = this.determineBestChoice(scores);\n\n    return {\n      documents,\n      scores,\n      bestChoice,\n      comparisonMatrix\n    };\n  }\n\n  // Helper methods for insurance field extraction\n  private static extractInsuranceField(text: string, keywords: string[]): string | null {\n    for (const keyword of keywords) {\n      const regex = new RegExp(`${keyword}[:\\\\s]*([^\\\\n\\\\r]{1,200})`, 'i');\n      const match = text.match(regex);\n      if (match && match[1]) {\n        return match[1].trim().replace(/[^\\w\\s\\d,.-]/g, '').substring(0, 100);\n      }\n    }\n    return null;\n  }\n\n  private static checkCoverageIncluded(text: string, keywords: string[]): string {\n    for (const keyword of keywords) {\n      if (text.includes(keyword.toLowerCase())) {\n        // Check if it's mentioned as included or covered\n        const context = this.getContextAroundKeyword(text, keyword, 50);\n        if (context.includes('included') || context.includes('covered') || context.includes('yes')) {\n          return 'Included';\n        } else if (context.includes('excluded') || context.includes('not covered') || context.includes('no')) {\n          return 'Excluded';\n        } else {\n          return 'Mentioned';\n        }\n      }\n    }\n    return 'Not specified';\n  }\n\n  private static checkExclusionPresent(text: string, keywords: string[]): string {\n    for (const keyword of keywords) {\n      if (text.includes(keyword.toLowerCase())) {\n        const context = this.getContextAroundKeyword(text, keyword, 50);\n        if (context.includes('excluded') || context.includes('not covered') || context.includes('exclusion')) {\n          return 'Present';\n        } else {\n          return 'Mentioned';\n        }\n      }\n    }\n    return 'Not present';\n  }\n\n  private static getContextAroundKeyword(text: string, keyword: string, contextLength: number): string {\n    const index = text.toLowerCase().indexOf(keyword.toLowerCase());\n    if (index === -1) return '';\n\n    const start = Math.max(0, index - contextLength);\n    const end = Math.min(text.length, index + keyword.length + contextLength);\n\n    return text.substring(start, end).toLowerCase();\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAGA,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IACxB,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAEO,MAAM;IACX,aAAa,YAAY,SAA6B,EAA0B;QAC9E,IAAI;YACF,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,qBAAqB,MAAM,IAAI,CAAC,mBAAmB,CAAC;YAE1D,OAAO;gBACL,IAAI,IAAI,CAAC,UAAU;gBACnB;gBACA,aAAa,mBAAmB,WAAW;gBAC3C,SAAS,mBAAmB,OAAO;gBACnC,iBAAiB,mBAAmB,eAAe;gBACnD,YAAY,IAAI,OAAO,WAAW;gBAClC,gBAAgB,IAAI,CAAC,oBAAoB,CAAC;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,aAAqB,oBAAoB,SAA6B,EAInE;QACD,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,sBAAsB,CAAC;YAE3C,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACtD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;YACjD,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,OAAO,IAAI,CAAC,0BAA0B,CAAC;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACvC;IACF;IAEA,OAAe,uBAAuB,SAA6B,EAAU;QAC3E,MAAM,oBAAoB,UAAU,GAAG,CAAC,CAAC,KAAK;YAC5C,MAAM,mBAAmB,IAAI,UAAU,EAAE;YACzC,OAAO,CAAC;SACL,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC;iBACrB,EAAE,IAAI,UAAU,EAAE,gBAAgB,UAAU;mBAC1C,EAAE,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,MAAM;qBACxC,EAAE,mBAAmB,KAAK,SAAS,CAAC,kBAAkB,MAAM,KAAK,iBAAiB;gBACvF,EAAE,IAAI,UAAU,EAAE,aAAa,KAAK,SAAS,OAAO;AACpE,CAAC;QACG,GAAG,IAAI,CAAC;QAER,OAAO,CAAC;sBACU,EAAE,UAAU,MAAM,CAAC;;AAEzC,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCpB,CAAC;IACC;IAEA,OAAe,2BAA2B,QAAa,EAIrD;QACA,OAAO;YACL,aAAa,MAAM,OAAO,CAAC,SAAS,WAAW,IAC3C,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBACzC,OAAO,KAAK,KAAK,IAAI;oBACrB,MAAM,KAAK,IAAI,IAAI;oBACnB,UAAU,KAAK,QAAQ;oBACvB,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY,IAAI;oBACnC,aAAa,KAAK,WAAW,IAAI;gBACnC,CAAC,KACC,EAAE;YACN,SAAS,SAAS,OAAO,IAAI;YAC7B,iBAAiB,MAAM,OAAO,CAAC,SAAS,eAAe,IAAI,SAAS,eAAe,GAAG,EAAE;QAC1F;IACF;IAEA,OAAe,yBAAyB,SAA6B,EAInE;QACA,MAAM,cAAsC,EAAE;QAE9C,qCAAqC;QACrC,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,gBAAgB;QAClE,IAAI,IAAI,IAAI,UAAU,IAAI,GAAG,GAAG;YAC9B,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,MAAM;gBACN,UAAU,QAAQ,CAAC,EAAE;gBACrB,UAAU,QAAQ,CAAC,EAAE;gBACrB,cAAc;gBACd,aAAa,CAAC,uBAAuB,EAAE,SAAS,IAAI,CAAC,SAAS;YAChE;QACF;QAEA,iCAAiC;QACjC,MAAM,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,CAAC,eAAe;QAC7D,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;QAC5E,IAAI,WAAW,KAAK;YAClB,YAAY,IAAI,CAAC;gBACf,OAAO;gBACP,MAAM;gBACN,UAAU,KAAK,CAAC,EAAE;gBAClB,UAAU,KAAK,CAAC,EAAE;gBAClB,cAAc;gBACd,aAAa,CAAC,2CAA2C,EAAE,WAAW,MAAM,UAAU,WAAW,UAAU,CAAC;YAC9G;QACF;QAEA,OAAO;YACL;YACA,SAAS,CAAC,oBAAoB,EAAE,UAAU,MAAM,CAAC,8CAA8C,CAAC;YAChG,iBAAiB;gBACf;gBACA;gBACA;aACD;QACH;IACF;IAEA,OAAe,qBAAqB,SAA6B,EAAoD;QACnH,MAAM,QAAQ,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,cAAc,MAAM,CAAC;QAEpE,IAAI,MAAM,KAAK,CAAC,CAAA,OAAQ,SAAS,cAAc,OAAO;QACtD,IAAI,MAAM,KAAK,CAAC,CAAA,OAAQ,SAAS,aAAa,OAAO;QACrD,IAAI,MAAM,KAAK,CAAC,CAAA,OAAQ,SAAS,YAAY,OAAO;QAEpD,OAAO;IACT;IAEA,OAAe,aAAqB;QAClC,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEA,gDAAgD;IAChD,aAAa,6BAA6B,SAA6B,EAAiC;QACtG,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,eAAe,CAAC;YAC1C,MAAM,mBAAmB,IAAI,CAAC,qBAAqB,CAAC;YACpD,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAE5C,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,IAAI,CAAC,wBAAwB,CAAC;QACvC;IACF;IAEA,aAAqB,gBAAgB,SAA6B,EAA6B;QAC7F,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,mBAAmB,CAAC;YAExC,MAAM,aAAa,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACtD,OAAO;gBACP,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;oBACA;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,aAAa;gBACb,YAAY;YACd;YAEA,MAAM,WAAW,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS;YACjD,IAAI,CAAC,UAAU;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,KAAK,KAAK,CAAC;YAC5B,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,MAAM,EAAE;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;IACF;IAEA,OAAe,oBAAoB,SAA6B,EAAU;QACxE,MAAM,oBAAoB,UAAU,GAAG,CAAC,CAAC,KAAK;YAC5C,MAAM,mBAAmB,IAAI,UAAU,EAAE;YACzC,OAAO,CAAC;SACL,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,QAAQ,CAAC;gBACtB,EAAE,kBAAkB,QAAQ,SAAS,gBAAgB,CAAC,EAAE,kBAAkB,QAAQ,YAAY,GAAG;UACvG,EAAE,kBAAkB,QAAQ,QAAQ,gBAAgB;eAC/C,EAAE,kBAAkB,cAAc,gBAAgB;eAClD,EAAE,kBAAkB,OAAO,UAAU,EAAE;SAC7C,EAAE,kBAAkB,OAAO,KAAK,SAAS,gBAAgB;mBAC/C,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,UAAU,CAAC;AACnF,CAAC;QACG,GAAG,IAAI,CAAC;QAER,OAAO,CAAC;iCACqB,EAAE,UAAU,MAAM,CAAC;;AAEpD,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CpB,CAAC;IACC;IAEA,oDAAoD;IACpD,aAAa,oBAAoB,UAAyB,EAA+B;QACvF,MAAM,cAAc,WAAW,WAAW;QAE1C,qDAAqD;QACrD,IAAI;QACJ,IAAI,WAAW,cAAc,KAAK,eAAe,WAAW,SAAS,CAAC,MAAM,IAAI,GAAG;YACjF,aAAa,MAAM,IAAI,CAAC,4BAA4B,CAAC,WAAW,SAAS;QAC3E;QAEA,OAAO;YACL,SAAS;gBACP,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC;gBACjH,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,OAAO;gBACL,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,KAAK,CAAC,QAAQ,CAAC;gBACnF,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,SAAS;gBACP,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC;gBACpF,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,UAAU;gBACR,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,KAAK,CAAC,QAAQ,CAAC;gBAClH,SAAS,IAAI,CAAC,oBAAoB,CAAC,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;YAC9E;YACA,SAAS;gBACP,gBAAgB,WAAW,OAAO;gBAClC,YAAY,IAAI,CAAC,mBAAmB,CAAC;gBACrC,aAAa,IAAI,CAAC,mBAAmB,CAAC;YACxC;YACA;QACF;IACF;IAEA,OAAe,qBAAqB,WAAmC,EAAU;QAC/E,IAAI,YAAY,MAAM,KAAK,GAAG,OAAO;QAErC,MAAM,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,QAAQ,MAAM;QAC5E,MAAM,eAAe,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,UAAU,MAAM;QAEhF,OAAO,CAAC,MAAM,EAAE,YAAY,MAAM,CAAC,gBAAgB,EAAE,WAAW,cAAc,EAAE,aAAa,cAAc,CAAC;IAC9G;IAEA,OAAe,oBAAoB,UAAyB,EAAsB;QAChF,6DAA6D;QAC7D,MAAM,kBAAkB,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;QAC9E,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAChC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,OAAe,oBAAoB,UAAyB,EAAY;QACtE,MAAM,QAAkB,EAAE;QAE1B,MAAM,kBAAkB,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK;QAC9E,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,CAAC,GAAG,gBAAgB,MAAM,CAAC,0CAA0C,CAAC;QAClF;QAEA,MAAM,eAAe,WAAW,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,QAAQ,CAAC;QACzE,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,OAAe,wBAAwB,MAAa,EAAE,SAA6B,EAAoB;QACrG,OAAO,UAAU,GAAG,CAAC,CAAC,KAAK;YACzB,MAAM,QAAQ,MAAM,CAAC,MAAM,IAAI,CAAC;YAChC,OAAO;gBACL,YAAY,IAAI,EAAE;gBAClB,UAAU,IAAI,QAAQ;gBACtB,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,UAAU,IAAI,IAAI,IAAI;gBAC1D,QAAQ;oBACN,SAAS,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,WAAW,IAAI,IAAI;oBAC5D,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,SAAS,IAAI,IAAI;oBACxD,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,UAAU,IAAI,IAAI;oBAC1D,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,YAAY,IAAI,IAAI;oBAC9D,cAAc,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,gBAAgB,IAAI,IAAI;gBACxE;gBACA,WAAW,MAAM,OAAO,CAAC,MAAM,SAAS,IAAI,MAAM,SAAS,GAAG;oBAAC;iBAAmB;gBAClF,YAAY,MAAM,OAAO,CAAC,MAAM,UAAU,IAAI,MAAM,UAAU,GAAG;oBAAC;iBAAmB;gBACrF,gBAAgB;oBAAC;oBAAQ;oBAAQ;oBAAc;iBAAO,CAAC,QAAQ,CAAC,MAAM,cAAc,IAChF,MAAM,cAAc,GAAG;YAC7B;QACF;IACF;IAEA,OAAe,qBAAqB,SAA6B,EAAoB;QACnF,OAAO,UAAU,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;gBACpC,YAAY,IAAI,EAAE;gBAClB,UAAU,IAAI,QAAQ;gBACtB,YAAY,KAAM,QAAQ;gBAC1B,QAAQ;oBACN,SAAS;oBACT,OAAO;oBACP,QAAQ;oBACR,UAAU;oBACV,cAAc;gBAChB;gBACA,WAAW;oBAAC;iBAAkC;gBAC9C,YAAY;oBAAC;iBAA0B;gBACvC,gBAAgB,UAAU,IAAI,SAAS;YACzC,CAAC;IACH;IAEA,OAAe,sBAAsB,SAA6B,EAAE;QAClE,MAAM,SAAgB,EAAE;QAExB,kDAAkD;QAClD,MAAM,SAAS;YACb,oBAAoB;YACpB;gBAAE,KAAK;gBAAsB,UAAU;gBAAW,OAAO;YAAqB;YAC9E;gBAAE,KAAK;gBAAkB,UAAU;gBAAW,OAAO;YAAsC;YAC3F;gBAAE,KAAK;gBAAc,UAAU;gBAAW,OAAO;YAAoC;YACrF;gBAAE,KAAK;gBAAoB,UAAU;gBAAS,OAAO;YAAmB;YACxE;gBAAE,KAAK;gBAAa,UAAU;gBAAS,OAAO;YAAY;YAC1D;gBAAE,KAAK;gBAAgB,UAAU;gBAAS,OAAO;YAAe;YAChE;gBAAE,KAAK;gBAAW,UAAU;gBAAS,OAAO;YAAU;YAEtD,4BAA4B;YAC5B;gBAAE,KAAK;gBAAwB,UAAU;gBAAY,OAAO;YAA2E;YACvI;gBAAE,KAAK;gBAAyB,UAAU;gBAAY,OAAO;YAAwB;YACrF;gBAAE,KAAK;gBAA2B,UAAU;gBAAY,OAAO;YAAkE;YACjI;gBAAE,KAAK;gBAAiB,UAAU;gBAAY,OAAO;YAAkF;YACvI;gBAAE,KAAK;gBAAkB,UAAU;gBAAY,OAAO;YAAuB;YAC7E;gBAAE,KAAK;gBAAoB,UAAU;gBAAY,OAAO;YAAsB;YAC9E;gBAAE,KAAK;gBAAyB,UAAU;gBAAY,OAAO;YAA6C;YAC1G;gBAAE,KAAK;gBAAqB,UAAU;gBAAY,OAAO;YAA0B;YACnF;gBAAE,KAAK;gBAAwB,UAAU;gBAAY,OAAO;YAAgC;YAC5F;gBAAE,KAAK;gBAAwB,UAAU;gBAAY,OAAO;YAAmC;YAC/F;gBAAE,KAAK;gBAAoB,UAAU;gBAAY,OAAO;YAAwF;YAChJ;gBAAE,KAAK;gBAAkB,UAAU;gBAAY,OAAO;YAA4C;YAClG;gBAAE,KAAK;gBAAuB,UAAU;gBAAY,OAAO;YAAuD;YAClH;gBAAE,KAAK;gBAAwB,UAAU;gBAAY,OAAO;YAAoC;YAChG;gBAAE,KAAK;gBAAwB,UAAU;gBAAY,OAAO;YAA2B;YACvF;gBAAE,KAAK;gBAAoB,UAAU;gBAAY,OAAO;YAAmC;YAC3F;gBAAE,KAAK;gBAAuB,UAAU;gBAAY,OAAO;YAAiC;YAC5F;gBAAE,KAAK;gBAAoB,UAAU;gBAAY,OAAO;YAAyB;YACjF;gBAAE,KAAK;gBAAa,UAAU;gBAAY,OAAO;YAAY;YAC7D;gBAAE,KAAK;gBAAwB,UAAU;gBAAY,OAAO;YAAuB;YACnF;gBAAE,KAAK;gBAAmB,UAAU;gBAAY,OAAO;YAAkB;YAEzE,mBAAmB;YACnB;gBAAE,KAAK;gBAAqB,UAAU;gBAAc,OAAO;YAAqB;YAChF;gBAAE,KAAK;gBAAsB,UAAU;gBAAc,OAAO;YAAqB;YACjF;gBAAE,KAAK;gBAAmB,UAAU;gBAAc,OAAO;YAAoB;SAC9E;QAED,OAAO,OAAO,CAAC,CAAA;YACb,MAAM,SAAS,UAAU,GAAG,CAAC,CAAA;gBAC3B,MAAM,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACjD,IAAI,QAAQ;gBAEZ,wDAAwD;gBACxD,OAAQ,MAAM,GAAG;oBACf,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAsB;4BAAmB;yBAAkB,KAAK;wBAC9G;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAkB;4BAAW;yBAAgB,KAAK;wBAChG;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAc;4BAAU;yBAAY,KAAK;wBACvF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAoB;yBAAc,KAAK;wBACrF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAa;4BAAsB;yBAAgB,KAAK;wBACtG;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAgB;4BAAiB;yBAAqB,KAAK;wBACzG;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAW;4BAAkB;yBAAuB,KAAK;wBACvG;oBAEF,sBAAsB;oBACtB,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAkB;4BAAsB;yBAAsB;wBAC5G;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAmB;yBAAgB;wBACjF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAW;4BAAmB;yBAAkB;wBAC9F;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAiB;yBAAuB;wBACtF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAkB;yBAAc;wBAC9E;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAe;yBAAsB;wBACnF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAyB;yBAAsB;wBAC7F;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAqB;yBAAa;wBAChF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAwB;yBAAa;wBACnF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAwB;yBAAgB;wBACtF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAwB;yBAAoB;wBAC1F;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAkB;yBAAa;wBAC7E;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAuB;yBAA2B;wBAChG;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAkB;yBAAoB;wBACpF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAwB;yBAA2B;wBACjG;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAoB;yBAAwB;wBAC1F;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAuB;yBAAmB;wBACxF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAoB;yBAAc;wBAChF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAa;yBAAc;wBACzE;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAiB;yBAAuB;wBACtF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAmB;yBAAuB;wBACxF;oBAEF,aAAa;oBACb,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAc;yBAAe;wBAC3E;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAsB;yBAAgB;wBACpF;oBACF,KAAK;wBACH,QAAQ,IAAI,CAAC,qBAAqB,CAAC,UAAU;4BAAC;4BAAS;4BAAa;yBAAsB;wBAC1F;gBACJ;gBAEA,OAAO;oBACL,YAAY,IAAI,EAAE;oBAClB;oBACA,QAAQ;oBACR,SAAS;oBACT,OAAO,GAAG,gBAAgB;gBAC5B;YACF;YAEA,wDAAwD;YACxD,IAAI,MAAM,QAAQ,KAAK,WAAW;gBAChC,2DAA2D;gBAC3D,MAAM,UAAU,OAAO,GAAG,CAAC,CAAA;oBACzB,MAAM,QAAQ,EAAE,KAAK,CAAC,KAAK,CAAC;oBAC5B,OAAO,QAAQ,WAAW,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,OAAO;gBAC1D;gBACA,MAAM,YAAY,KAAK,GAAG,IAAI;gBAC9B,MAAM,YAAY,KAAK,GAAG,IAAI;gBAE9B,OAAO,OAAO,CAAC,CAAC,GAAG;oBACjB,IAAI,MAAM,GAAG,KAAK,cAAc;wBAC9B,kCAAkC;wBAClC,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,UAAU,EAAE,MAAM,GAAG;wBACnE,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,YAAY,cAAc,WAAW,EAAE,OAAO,GAAG;oBACjG,OAAO,IAAI,MAAM,GAAG,KAAK,sBAAsB;wBAC7C,wCAAwC;wBACxC,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,UAAU,EAAE,MAAM,GAAG;wBACnE,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,YAAY,cAAc,WAAW,EAAE,OAAO,GAAG;oBACjG,OAAO;wBACL,+BAA+B;wBAC/B,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,UAAU,EAAE,MAAM,GAAG;wBACnE,IAAI,OAAO,CAAC,EAAE,KAAK,aAAa,cAAc,YAAY,cAAc,WAAW,EAAE,OAAO,GAAG;oBACjG;oBACA,EAAE,KAAK,GAAG,OAAO,CAAC,EAAE,KAAK,WAAW,KAAK,KAAK,KAAK,CAAC,MAAM,AAAC,CAAC,OAAO,CAAC,EAAE,GAAG,SAAS,IAAI,CAAC,YAAY,SAAS,IAAK;gBACnH;YACF,OAAO,IAAI,MAAM,QAAQ,KAAK,YAAY;gBACxC,0DAA0D;gBAC1D,OAAO,OAAO,CAAC,CAAA;oBACb,IAAI,EAAE,KAAK,KAAK,YAAY;wBAC1B,EAAE,MAAM,GAAG;wBACX,EAAE,KAAK,GAAG;oBACZ,OAAO,IAAI,EAAE,KAAK,KAAK,YAAY;wBACjC,EAAE,OAAO,GAAG;wBACZ,EAAE,KAAK,GAAG;oBACZ,OAAO;wBACL,EAAE,KAAK,GAAG;oBACZ;gBACF;YACF,OAAO,IAAI,MAAM,QAAQ,KAAK,cAAc;gBAC1C,yDAAyD;gBACzD,OAAO,OAAO,CAAC,CAAA;oBACb,IAAI,EAAE,KAAK,KAAK,eAAe;wBAC7B,EAAE,MAAM,GAAG;wBACX,EAAE,KAAK,GAAG;oBACZ,OAAO,IAAI,EAAE,KAAK,KAAK,WAAW;wBAChC,EAAE,OAAO,GAAG;wBACZ,EAAE,KAAK,GAAG;oBACZ,OAAO;wBACL,EAAE,KAAK,GAAG;oBACZ;gBACF;YACF,OAAO;gBACL,0CAA0C;gBAC1C,MAAM,aAAa,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;gBAChD,IAAI,YAAY,WAAW,MAAM,GAAG;gBAEpC,OAAO,OAAO,CAAC,CAAA;oBACb,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,kBAAkB,KAAK;gBAC/C;YACF;YAEA,OAAO,IAAI,CAAC;gBACV,OAAO,MAAM,KAAK;gBAClB,UAAU,MAAM,QAAQ;gBACxB;YACF;QACF;QAEA,OAAO;IACT;IAEA,OAAe,oBAAoB,MAAwB,EAAE;QAC3D,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,MAAM,UACrC,QAAQ,UAAU,GAAG,KAAK,UAAU,GAAG,UAAU;QAGnD,OAAO;YACL,YAAY,UAAU,UAAU;YAChC,QAAQ,CAAC,iDAAiD,EAAE,UAAU,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU;YAC3G,YAAY,KAAK,GAAG,CAAC,UAAU,UAAU,GAAG,KAAK;QACnD;IACF;IAEA,OAAe,yBAAyB,SAA6B,EAAwB;QAC3F,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC;QACzC,MAAM,mBAAmB,IAAI,CAAC,qBAAqB,CAAC;QACpD,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;QAE5C,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,gDAAgD;IAChD,OAAe,sBAAsB,IAAY,EAAE,QAAkB,EAAiB;QACpF,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,IAAI,OAAO,GAAG,QAAQ,yBAAyB,CAAC,EAAE;YAChE,MAAM,QAAQ,KAAK,KAAK,CAAC;YACzB,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE;gBACrB,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAC,GAAG;YACnE;QACF;QACA,OAAO;IACT;IAEA,OAAe,sBAAsB,IAAY,EAAE,QAAkB,EAAU;QAC7E,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI,KAAK,QAAQ,CAAC,QAAQ,WAAW,KAAK;gBACxC,iDAAiD;gBACjD,MAAM,UAAU,IAAI,CAAC,uBAAuB,CAAC,MAAM,SAAS;gBAC5D,IAAI,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,QAAQ;oBAC1F,OAAO;gBACT,OAAO,IAAI,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,OAAO;oBACpG,OAAO;gBACT,OAAO;oBACL,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,OAAe,sBAAsB,IAAY,EAAE,QAAkB,EAAU;QAC7E,KAAK,MAAM,WAAW,SAAU;YAC9B,IAAI,KAAK,QAAQ,CAAC,QAAQ,WAAW,KAAK;gBACxC,MAAM,UAAU,IAAI,CAAC,uBAAuB,CAAC,MAAM,SAAS;gBAC5D,IAAI,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,cAAc;oBACpG,OAAO;gBACT,OAAO;oBACL,OAAO;gBACT;YACF;QACF;QACA,OAAO;IACT;IAEA,OAAe,wBAAwB,IAAY,EAAE,OAAe,EAAE,aAAqB,EAAU;QACnG,MAAM,QAAQ,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ,WAAW;QAC5D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,QAAQ;QAClC,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,QAAQ,QAAQ,MAAM,GAAG;QAE3D,OAAO,KAAK,SAAS,CAAC,OAAO,KAAK,WAAW;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/api/compare-pdfs/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { ExtractedPDFData, PDFComparison } from '@/types/pdf';\nimport { PDFComparator } from '@/services/pdfComparator';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { documents } = body;\n\n    if (!documents || !Array.isArray(documents) || documents.length < 2) {\n      return NextResponse.json(\n        { error: 'At least 2 documents are required for comparison' },\n        { status: 400 }\n      );\n    }\n\n    if (documents.length > 5) {\n      return NextResponse.json(\n        { error: 'Maximum 5 documents can be compared at once' },\n        { status: 400 }\n      );\n    }\n\n    // Validate document structure\n    for (const doc of documents) {\n      if (!doc.id || !doc.fileName || !doc.content) {\n        return NextResponse.json(\n          { error: 'Invalid document structure' },\n          { status: 400 }\n        );\n      }\n    }\n\n    const comparison = await PDFComparator.comparePDFs(documents);\n    \n    return NextResponse.json(comparison);\n  } catch (error) {\n    console.error('PDF comparison error:', error);\n    return NextResponse.json(\n      { error: 'Failed to compare PDFs. Please try again.' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,GAAG;QAEtB,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,cAAc,UAAU,MAAM,GAAG,GAAG;YACnE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmD,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8C,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,KAAK,MAAM,OAAO,UAAW;YAC3B,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,IAAI,OAAO,EAAE;gBAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA6B,GACtC;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,MAAM,aAAa,MAAM,kIAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;QAEnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4C,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}