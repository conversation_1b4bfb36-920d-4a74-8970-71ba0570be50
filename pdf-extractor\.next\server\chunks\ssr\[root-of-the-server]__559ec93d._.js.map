{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/PDFUploader.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, FileText, AlertCircle } from 'lucide-react';\n\ninterface PDFUploaderProps {\n  onFileUpload: (file: File) => void;\n  isProcessing: boolean;\n}\n\nexport default function PDFUploader({ onFileUpload, isProcessing }: PDFUploaderProps) {\n  const [error, setError] = useState<string | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {\n    setError(null);\n    \n    if (rejectedFiles.length > 0) {\n      setError('Please upload a valid PDF file.');\n      return;\n    }\n\n    if (acceptedFiles.length > 0) {\n      const file = acceptedFiles[0];\n      if (file.size > 10 * 1024 * 1024) { // 10MB limit\n        setError('File size must be less than 10MB.');\n        return;\n      }\n      onFileUpload(file);\n    }\n  }, [onFileUpload]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'application/pdf': ['.pdf']\n    },\n    multiple: false,\n    disabled: isProcessing\n  });\n\n  return (\n    <div className=\"w-full max-w-2xl mx-auto\">\n      <div\n        {...getRootProps()}\n        className={`\n          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\n          ${isDragActive \n            ? 'border-blue-500 bg-blue-50' \n            : 'border-gray-300 hover:border-gray-400'\n          }\n          ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}\n        `}\n      >\n        <input {...getInputProps()} />\n        \n        <div className=\"flex flex-col items-center space-y-4\">\n          {isProcessing ? (\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n          ) : (\n            <Upload className=\"h-12 w-12 text-gray-400\" />\n          )}\n          \n          <div>\n            <p className=\"text-lg font-medium text-gray-700\">\n              {isProcessing \n                ? 'Processing PDF...' \n                : isDragActive \n                  ? 'Drop the PDF file here' \n                  : 'Drag & drop a PDF file here'\n              }\n            </p>\n            {!isProcessing && (\n              <p className=\"text-sm text-gray-500 mt-1\">\n                or click to select a file (max 10MB)\n              </p>\n            )}\n          </div>\n          \n          <FileText className=\"h-8 w-8 text-gray-300\" />\n        </div>\n      </div>\n      \n      {error && (\n        <div className=\"mt-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center space-x-2\">\n          <AlertCircle className=\"h-5 w-5 text-red-500\" />\n          <span className=\"text-red-700 text-sm\">{error}</span>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAJA;;;;;AAWe,SAAS,YAAY,EAAE,YAAY,EAAE,YAAY,EAAoB;IAClF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,eAAuB;QACjD,SAAS;QAET,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,SAAS;YACT;QACF;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;gBAChC,SAAS;gBACT;YACF;YACA,aAAa;QACf;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,mBAAmB;gBAAC;aAAO;QAC7B;QACA,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;UAEV,EAAE,eACE,+BACA,wCACH;UACD,EAAE,eAAe,kCAAkC,GAAG;QACxD,CAAC;;kCAED,8OAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC;gCAAI,WAAU;;;;;qDAEf,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAGpB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDACV,eACG,sBACA,eACE,2BACA;;;;;;oCAGP,CAAC,8BACA,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM9C,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAIvB,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAKlD", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/components/PDFResults.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { ExtractedPDFData } from '@/types/pdf';\nimport { \n  FileText, \n  Info, \n  BarChart3, \n  Download, \n  Copy, \n  ChevronDown, \n  ChevronRight,\n  Clock,\n  User,\n  Calendar,\n  Hash\n} from 'lucide-react';\n\ninterface PDFResultsProps {\n  data: ExtractedPDFData;\n  onExport: (format: 'json' | 'csv' | 'txt') => void;\n}\n\nexport default function PDFResults({ data, onExport }: PDFResultsProps) {\n  const [activeTab, setActiveTab] = useState<'metadata' | 'content' | 'statistics'>('metadata');\n  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({\n    headings: true,\n    paragraphs: false,\n    tables: false,\n    lists: false\n  });\n\n  const toggleSection = (section: string) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [section]: !prev[section]\n    }));\n  };\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text);\n  };\n\n  const tabs = [\n    { id: 'metadata', label: 'Metadata', icon: Info },\n    { id: 'content', label: 'Content', icon: FileText },\n    { id: 'statistics', label: 'Statistics', icon: BarChart3 }\n  ];\n\n  return (\n    <div className=\"w-full max-w-6xl mx-auto bg-white rounded-lg shadow-lg\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-6\">\n        <div className=\"flex justify-between items-start\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">{data.fileName}</h2>\n            <p className=\"text-sm text-gray-500 mt-1\">\n              Extracted on {new Date(data.extractedAt).toLocaleString()}\n            </p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={() => onExport('json')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>JSON</span>\n            </button>\n            <button\n              onClick={() => onExport('csv')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>CSV</span>\n            </button>\n            <button\n              onClick={() => onExport('txt')}\n              className=\"flex items-center space-x-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n            >\n              <Download className=\"h-4 w-4\" />\n              <span>TXT</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200\">\n        <nav className=\"flex space-x-8 px-6\">\n          {tabs.map((tab) => {\n            const Icon = tab.icon;\n            return (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id as any)}\n                className={`\n                  flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-colors\n                  ${activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                  }\n                `}\n              >\n                <Icon className=\"h-4 w-4\" />\n                <span>{tab.label}</span>\n              </button>\n            );\n          })}\n        </nav>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        {activeTab === 'metadata' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <MetadataItem icon={FileText} label=\"Title\" value={data.metadata.title} />\n              <MetadataItem icon={User} label=\"Author\" value={data.metadata.author} />\n              <MetadataItem icon={Info} label=\"Subject\" value={data.metadata.subject} />\n              <MetadataItem icon={FileText} label=\"Creator\" value={data.metadata.creator} />\n            </div>\n            <div className=\"space-y-4\">\n              <MetadataItem icon={FileText} label=\"Producer\" value={data.metadata.producer} />\n              <MetadataItem icon={Calendar} label=\"Created\" value={data.metadata.creationDate} />\n              <MetadataItem icon={Calendar} label=\"Modified\" value={data.metadata.modificationDate} />\n              <MetadataItem icon={Hash} label=\"Pages\" value={data.metadata.pages?.toString()} />\n            </div>\n            {data.metadata.keywords && data.metadata.keywords !== 'None' && (\n              <div className=\"md:col-span-2\">\n                <MetadataItem icon={Hash} label=\"Keywords\" value={data.metadata.keywords} />\n              </div>\n            )}\n          </div>\n        )}\n\n        {activeTab === 'content' && (\n          <div className=\"space-y-6\">\n            {/* Structure Sections */}\n            {Object.entries(data.content.structure).map(([key, items]) => (\n              <StructureSection\n                key={key}\n                title={key.charAt(0).toUpperCase() + key.slice(1)}\n                items={items}\n                isExpanded={expandedSections[key]}\n                onToggle={() => toggleSection(key)}\n                onCopy={() => copyToClipboard(items.join('\\n'))}\n              />\n            ))}\n            \n            {/* Full Text */}\n            <div className=\"border rounded-lg\">\n              <button\n                onClick={() => toggleSection('fullText')}\n                className=\"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\"\n              >\n                <h3 className=\"text-lg font-semibold text-gray-900\">Full Text</h3>\n                <div className=\"flex items-center space-x-2\">\n                  <button\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      copyToClipboard(data.content.fullText);\n                    }}\n                    className=\"p-1 hover:bg-gray-200 rounded\"\n                  >\n                    <Copy className=\"h-4 w-4\" />\n                  </button>\n                  {expandedSections.fullText ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n                </div>\n              </button>\n              {expandedSections.fullText && (\n                <div className=\"p-4 border-t\">\n                  <pre className=\"whitespace-pre-wrap text-sm text-gray-700 max-h-96 overflow-y-auto\">\n                    {data.content.fullText}\n                  </pre>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'statistics' && (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <StatCard\n              icon={FileText}\n              label=\"Total Pages\"\n              value={data.statistics.totalPages.toLocaleString()}\n              color=\"blue\"\n            />\n            <StatCard\n              icon={Hash}\n              label=\"Total Words\"\n              value={data.statistics.totalWords.toLocaleString()}\n              color=\"green\"\n            />\n            <StatCard\n              icon={BarChart3}\n              label=\"Total Characters\"\n              value={data.statistics.totalCharacters.toLocaleString()}\n              color=\"purple\"\n            />\n            <StatCard\n              icon={Clock}\n              label=\"Processing Time\"\n              value={`${data.statistics.processingTime}ms`}\n              color=\"orange\"\n            />\n            <StatCard\n              icon={FileText}\n              label=\"File Size\"\n              value={data.statistics.fileSize}\n              color=\"red\"\n            />\n            <StatCard\n              icon={BarChart3}\n              label=\"Avg Words/Page\"\n              value={data.statistics.averageWordsPerPage.toLocaleString()}\n              color=\"indigo\"\n            />\n            <StatCard\n              icon={Hash}\n              label=\"Total Lines\"\n              value={data.statistics.totalLines.toLocaleString()}\n              color=\"pink\"\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n\n// Helper Components\nfunction MetadataItem({ icon: Icon, label, value }: { icon: any, label: string, value?: string }) {\n  return (\n    <div className=\"flex items-start space-x-3\">\n      <Icon className=\"h-5 w-5 text-gray-400 mt-0.5\" />\n      <div>\n        <p className=\"text-sm font-medium text-gray-900\">{label}</p>\n        <p className=\"text-sm text-gray-600\">{value || 'Not available'}</p>\n      </div>\n    </div>\n  );\n}\n\nfunction StructureSection({ \n  title, \n  items, \n  isExpanded, \n  onToggle, \n  onCopy \n}: { \n  title: string, \n  items: string[], \n  isExpanded: boolean, \n  onToggle: () => void, \n  onCopy: () => void \n}) {\n  if (items.length === 0) return null;\n\n  return (\n    <div className=\"border rounded-lg\">\n      <button\n        onClick={onToggle}\n        className=\"w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors\"\n      >\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title} ({items.length})</h3>\n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={(e) => {\n              e.stopPropagation();\n              onCopy();\n            }}\n            className=\"p-1 hover:bg-gray-200 rounded\"\n          >\n            <Copy className=\"h-4 w-4\" />\n          </button>\n          {isExpanded ? <ChevronDown className=\"h-5 w-5\" /> : <ChevronRight className=\"h-5 w-5\" />}\n        </div>\n      </button>\n      {isExpanded && (\n        <div className=\"p-4 border-t\">\n          <ul className=\"space-y-2\">\n            {items.map((item, index) => (\n              <li key={index} className=\"text-sm text-gray-700 p-2 bg-gray-50 rounded\">\n                {item}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction StatCard({ \n  icon: Icon, \n  label, \n  value, \n  color \n}: { \n  icon: any, \n  label: string, \n  value: string, \n  color: string \n}) {\n  const colorClasses = {\n    blue: 'bg-blue-50 text-blue-600',\n    green: 'bg-green-50 text-green-600',\n    purple: 'bg-purple-50 text-purple-600',\n    orange: 'bg-orange-50 text-orange-600',\n    red: 'bg-red-50 text-red-600',\n    indigo: 'bg-indigo-50 text-indigo-600',\n    pink: 'bg-pink-50 text-pink-600'\n  };\n\n  return (\n    <div className=\"bg-white border rounded-lg p-4\">\n      <div className={`inline-flex p-2 rounded-lg ${colorClasses[color as keyof typeof colorClasses]}`}>\n        <Icon className=\"h-5 w-5\" />\n      </div>\n      <p className=\"text-2xl font-bold text-gray-900 mt-2\">{value}</p>\n      <p className=\"text-sm text-gray-600\">{label}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAuBe,SAAS,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAmB;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyC;IAClF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;QAChF,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAC3B,CAAC;IACH;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC;IAChC;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;QAChD;YAAE,IAAI;YAAW,OAAO;YAAW,MAAM,8MAAA,CAAA,WAAQ;QAAC;QAClD;YAAE,IAAI;YAAc,OAAO;YAAc,MAAM,kNAAA,CAAA,YAAS;QAAC;KAC1D;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC,KAAK,QAAQ;;;;;;8CAC/D,8OAAC;oCAAE,WAAU;;wCAA6B;wCAC1B,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc;;;;;;;;;;;;;sCAG3D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCACC,SAAS,IAAM,SAAS;oCACxB,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC;wBACT,MAAM,OAAO,IAAI,IAAI;wBACrB,qBACE,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC;;kBAEV,EAAE,cAAc,IAAI,EAAE,GAClB,kCACA,uDACH;gBACH,CAAC;;8CAED,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAM,IAAI,KAAK;;;;;;;2BAXX,IAAI,EAAE;;;;;oBAcjB;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAa,MAAM,8MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAQ,OAAO,KAAK,QAAQ,CAAC,KAAK;;;;;;kDACtE,8OAAC;wCAAa,MAAM,kMAAA,CAAA,OAAI;wCAAE,OAAM;wCAAS,OAAO,KAAK,QAAQ,CAAC,MAAM;;;;;;kDACpE,8OAAC;wCAAa,MAAM,kMAAA,CAAA,OAAI;wCAAE,OAAM;wCAAU,OAAO,KAAK,QAAQ,CAAC,OAAO;;;;;;kDACtE,8OAAC;wCAAa,MAAM,8MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAU,OAAO,KAAK,QAAQ,CAAC,OAAO;;;;;;;;;;;;0CAE5E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAa,MAAM,8MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAW,OAAO,KAAK,QAAQ,CAAC,QAAQ;;;;;;kDAC5E,8OAAC;wCAAa,MAAM,0MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAU,OAAO,KAAK,QAAQ,CAAC,YAAY;;;;;;kDAC/E,8OAAC;wCAAa,MAAM,0MAAA,CAAA,WAAQ;wCAAE,OAAM;wCAAW,OAAO,KAAK,QAAQ,CAAC,gBAAgB;;;;;;kDACpF,8OAAC;wCAAa,MAAM,kMAAA,CAAA,OAAI;wCAAE,OAAM;wCAAQ,OAAO,KAAK,QAAQ,CAAC,KAAK,EAAE;;;;;;;;;;;;4BAErE,KAAK,QAAQ,CAAC,QAAQ,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,wBACpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAa,MAAM,kMAAA,CAAA,OAAI;oCAAE,OAAM;oCAAW,OAAO,KAAK,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;;;;;oBAM/E,cAAc,2BACb,8OAAC;wBAAI,WAAU;;4BAEZ,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACvD,8OAAC;oCAEC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;oCAC/C,OAAO;oCACP,YAAY,gBAAgB,CAAC,IAAI;oCACjC,UAAU,IAAM,cAAc;oCAC9B,QAAQ,IAAM,gBAAgB,MAAM,IAAI,CAAC;mCALpC;;;;;0CAUT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,gBAAgB,KAAK,OAAO,CAAC,QAAQ;wDACvC;wDACA,WAAU;kEAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;oDAEjB,iBAAiB,QAAQ,iBAAG,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAAe,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;oCAG9F,iBAAiB,QAAQ,kBACxB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;oBAQjC,cAAc,8BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAM,8MAAA,CAAA,WAAQ;gCACd,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,cAAc;gCAChD,OAAM;;;;;;0CAER,8OAAC;gCACC,MAAM,kMAAA,CAAA,OAAI;gCACV,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,cAAc;gCAChD,OAAM;;;;;;0CAER,8OAAC;gCACC,MAAM,kNAAA,CAAA,YAAS;gCACf,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,eAAe,CAAC,cAAc;gCACrD,OAAM;;;;;;0CAER,8OAAC;gCACC,MAAM,oMAAA,CAAA,QAAK;gCACX,OAAM;gCACN,OAAO,GAAG,KAAK,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gCAC5C,OAAM;;;;;;0CAER,8OAAC;gCACC,MAAM,8MAAA,CAAA,WAAQ;gCACd,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,QAAQ;gCAC/B,OAAM;;;;;;0CAER,8OAAC;gCACC,MAAM,kNAAA,CAAA,YAAS;gCACf,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,mBAAmB,CAAC,cAAc;gCACzD,OAAM;;;;;;0CAER,8OAAC;gCACC,MAAM,kMAAA,CAAA,OAAI;gCACV,OAAM;gCACN,OAAO,KAAK,UAAU,CAAC,UAAU,CAAC,cAAc;gCAChD,OAAM;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;AAEA,oBAAoB;AACpB,SAAS,aAAa,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,KAAK,EAAgD;IAC9F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,WAAU;;;;;;0BAChB,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAqC;;;;;;kCAClD,8OAAC;wBAAE,WAAU;kCAAyB,SAAS;;;;;;;;;;;;;;;;;;AAIvD;AAEA,SAAS,iBAAiB,EACxB,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,MAAM,EAOP;IACC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;;4BAAuC;4BAAM;4BAAG,MAAM,MAAM;4BAAC;;;;;;;kCAC3E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB;gCACF;gCACA,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;4BAEjB,2BAAa,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAAe,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;YAG/E,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAAe,WAAU;sCACvB;2BADM;;;;;;;;;;;;;;;;;;;;;AASvB;AAEA,SAAS,SAAS,EAChB,MAAM,IAAI,EACV,KAAK,EACL,KAAK,EACL,KAAK,EAMN;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,CAAC,2BAA2B,EAAE,YAAY,CAAC,MAAmC,EAAE;0BAC9F,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,8OAAC;gBAAE,WAAU;0BAAyC;;;;;;0BACtD,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/pdfExtractor.ts"], "sourcesContent": ["import { ExtractedPDFData } from '@/types/pdf';\n\nexport class PDFExtractorService {\n  static async extractPDFData(file: File): Promise<ExtractedPDFData> {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const response = await fetch('/api/extract-pdf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to extract PDF data');\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.error('PDF extraction error:', error);\n      throw new Error(error instanceof Error ? error.message : 'Failed to extract PDF data. Please ensure the file is a valid PDF.');\n    }\n  }\n\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,aAAa,eAAe,IAAU,EAA6B;QACjE,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3D;IACF;AAEF", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/services/exportService.ts"], "sourcesContent": ["import { ExtractedPDFData } from '@/types/pdf';\n\nexport class ExportService {\n  static exportAsJSON(data: ExtractedPDFData): void {\n    const jsonString = JSON.stringify(data, null, 2);\n    this.downloadFile(jsonString, `${data.fileName}_extracted.json`, 'application/json');\n  }\n\n  static exportAsCSV(data: ExtractedPDFData): void {\n    const csvContent = this.convertToCSV(data);\n    this.downloadFile(csvContent, `${data.fileName}_extracted.csv`, 'text/csv');\n  }\n\n  static exportAsText(data: ExtractedPDFData): void {\n    const textContent = this.convertToText(data);\n    this.downloadFile(textContent, `${data.fileName}_extracted.txt`, 'text/plain');\n  }\n\n  private static convertToCSV(data: ExtractedPDFData): string {\n    const rows: string[] = [];\n    \n    // Header\n    rows.push('Category,Field,Value');\n    \n    // Metadata\n    Object.entries(data.metadata).forEach(([key, value]) => {\n      rows.push(`Metadata,${key},\"${String(value).replace(/\"/g, '\"\"')}\"`);\n    });\n    \n    // Statistics\n    Object.entries(data.statistics).forEach(([key, value]) => {\n      rows.push(`Statistics,${key},\"${String(value).replace(/\"/g, '\"\"')}\"`);\n    });\n    \n    // Content structure counts\n    Object.entries(data.content.structure).forEach(([key, items]) => {\n      rows.push(`Content Structure,${key} Count,${items.length}`);\n    });\n    \n    // Add some sample content\n    rows.push(`Content,Full Text Length,${data.content.fullText.length}`);\n    rows.push(`Content,Sample Text,\"${data.content.fullText.substring(0, 200).replace(/\"/g, '\"\"')}...\"`);\n    \n    return rows.join('\\n');\n  }\n\n  private static convertToText(data: ExtractedPDFData): string {\n    const sections: string[] = [];\n    \n    // Header\n    sections.push('PDF EXTRACTION REPORT');\n    sections.push('='.repeat(50));\n    sections.push(`File: ${data.fileName}`);\n    sections.push(`Extracted: ${new Date(data.extractedAt).toLocaleString()}`);\n    sections.push('');\n    \n    // Metadata\n    sections.push('METADATA');\n    sections.push('-'.repeat(20));\n    Object.entries(data.metadata).forEach(([key, value]) => {\n      sections.push(`${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}`);\n    });\n    sections.push('');\n    \n    // Statistics\n    sections.push('STATISTICS');\n    sections.push('-'.repeat(20));\n    Object.entries(data.statistics).forEach(([key, value]) => {\n      const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());\n      sections.push(`${label}: ${value}`);\n    });\n    sections.push('');\n    \n    // Content Structure\n    sections.push('CONTENT STRUCTURE');\n    sections.push('-'.repeat(20));\n    Object.entries(data.content.structure).forEach(([key, items]) => {\n      if (items.length > 0) {\n        sections.push(`${key.toUpperCase()} (${items.length} items):`);\n        items.slice(0, 5).forEach((item, index) => {\n          sections.push(`  ${index + 1}. ${item.substring(0, 100)}${item.length > 100 ? '...' : ''}`);\n        });\n        if (items.length > 5) {\n          sections.push(`  ... and ${items.length - 5} more items`);\n        }\n        sections.push('');\n      }\n    });\n    \n    // Full Text (truncated)\n    sections.push('FULL TEXT (First 1000 characters)');\n    sections.push('-'.repeat(40));\n    sections.push(data.content.fullText.substring(0, 1000));\n    if (data.content.fullText.length > 1000) {\n      sections.push('\\n... (text truncated)');\n    }\n    \n    return sections.join('\\n');\n  }\n\n  private static downloadFile(content: string, filename: string, mimeType: string): void {\n    const blob = new Blob([content], { type: mimeType });\n    const url = URL.createObjectURL(blob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    \n    // Clean up the URL object\n    URL.revokeObjectURL(url);\n  }\n}\n"], "names": [], "mappings": ";;;AAEO,MAAM;IACX,OAAO,aAAa,IAAsB,EAAQ;QAChD,MAAM,aAAa,KAAK,SAAS,CAAC,MAAM,MAAM;QAC9C,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,QAAQ,CAAC,eAAe,CAAC,EAAE;IACnE;IAEA,OAAO,YAAY,IAAsB,EAAQ;QAC/C,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC,EAAE;IAClE;IAEA,OAAO,aAAa,IAAsB,EAAQ;QAChD,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC;QACvC,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC,EAAE;IACnE;IAEA,OAAe,aAAa,IAAsB,EAAU;QAC1D,MAAM,OAAiB,EAAE;QAEzB,SAAS;QACT,KAAK,IAAI,CAAC;QAEV,WAAW;QACX,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACjD,KAAK,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,EAAE,OAAO,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;QACpE;QAEA,aAAa;QACb,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACnD,KAAK,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE,OAAO,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;QACtE;QAEA,2BAA2B;QAC3B,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1D,KAAK,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,OAAO,EAAE,MAAM,MAAM,EAAE;QAC5D;QAEA,0BAA0B;QAC1B,KAAK,IAAI,CAAC,CAAC,yBAAyB,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE;QACpE,KAAK,IAAI,CAAC,CAAC,qBAAqB,EAAE,KAAK,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,OAAO,CAAC,MAAM,MAAM,IAAI,CAAC;QAEnG,OAAO,KAAK,IAAI,CAAC;IACnB;IAEA,OAAe,cAAc,IAAsB,EAAU;QAC3D,MAAM,WAAqB,EAAE;QAE7B,SAAS;QACT,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,SAAS,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,QAAQ,EAAE;QACtC,SAAS,IAAI,CAAC,CAAC,WAAW,EAAE,IAAI,KAAK,KAAK,WAAW,EAAE,cAAc,IAAI;QACzE,SAAS,IAAI,CAAC;QAEd,WAAW;QACX,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,OAAO,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACjD,SAAS,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,EAAE,EAAE,OAAO;QACzE;QACA,SAAS,IAAI,CAAC;QAEd,aAAa;QACb,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,OAAO,OAAO,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACnD,MAAM,QAAQ,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;YACjF,SAAS,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO;QACpC;QACA,SAAS,IAAI,CAAC;QAEd,oBAAoB;QACpB,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1D,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,SAAS,IAAI,CAAC,GAAG,IAAI,WAAW,GAAG,EAAE,EAAE,MAAM,MAAM,CAAC,QAAQ,CAAC;gBAC7D,MAAM,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,MAAM;oBAC/B,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,MAAM,GAAG,MAAM,QAAQ,IAAI;gBAC5F;gBACA,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,SAAS,IAAI,CAAC,CAAC,UAAU,EAAE,MAAM,MAAM,GAAG,EAAE,WAAW,CAAC;gBAC1D;gBACA,SAAS,IAAI,CAAC;YAChB;QACF;QAEA,wBAAwB;QACxB,SAAS,IAAI,CAAC;QACd,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC;QACzB,SAAS,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG;QACjD,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM;YACvC,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO,SAAS,IAAI,CAAC;IACvB;IAEA,OAAe,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAQ;QACrF,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAS;QAClD,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,0BAA0B;QAC1B,IAAI,eAAe,CAAC;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport PDFUploader from '@/components/PDFUploader';\nimport PDFResults from '@/components/PDFResults';\nimport { PDFExtractorService } from '@/services/pdfExtractor';\nimport { ExportService } from '@/services/exportService';\nimport { ExtractedPDFData } from '@/types/pdf';\nimport { FileText, Zap, Shield, Download } from 'lucide-react';\n\nexport default function Home() {\n  const [extractedData, setExtractedData] = useState<ExtractedPDFData | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleFileUpload = async (file: File) => {\n    setIsProcessing(true);\n    setError(null);\n    setExtractedData(null);\n\n    try {\n      const data = await PDFExtractorService.extractPDFData(file);\n      setExtractedData(data);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred while processing the PDF');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleExport = (format: 'json' | 'csv' | 'txt') => {\n    if (!extractedData) return;\n\n    switch (format) {\n      case 'json':\n        ExportService.exportAsJSON(extractedData);\n        break;\n      case 'csv':\n        ExportService.exportAsCSV(extractedData);\n        break;\n      case 'txt':\n        ExportService.exportAsText(extractedData);\n        break;\n    }\n  };\n\n  const resetApp = () => {\n    setExtractedData(null);\n    setError(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"bg-blue-600 p-2 rounded-lg\">\n                <FileText className=\"h-8 w-8 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900\">PDF Info Extractor</h1>\n                <p className=\"text-gray-600\">Extract structured information from PDF documents</p>\n              </div>\n            </div>\n            {extractedData && (\n              <button\n                onClick={resetApp}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors\"\n              >\n                New PDF\n              </button>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {!extractedData && !isProcessing && (\n          <>\n            {/* Features Section */}\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Most Effective PDF Information Extractor\n              </h2>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-3xl mx-auto\">\n                Upload any PDF document and get comprehensive, structured information including metadata,\n                content analysis, and detailed statistics in seconds.\n              </p>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\">\n                <FeatureCard\n                  icon={Zap}\n                  title=\"Lightning Fast\"\n                  description=\"Extract information from PDFs in seconds with our optimized processing engine\"\n                />\n                <FeatureCard\n                  icon={Shield}\n                  title=\"Secure & Private\"\n                  description=\"All processing happens locally in your browser. Your files never leave your device\"\n                />\n                <FeatureCard\n                  icon={Download}\n                  title=\"Multiple Formats\"\n                  description=\"Export extracted data as JSON, CSV, or formatted text for further analysis\"\n                />\n              </div>\n            </div>\n\n            {/* Upload Section */}\n            <PDFUploader onFileUpload={handleFileUpload} isProcessing={isProcessing} />\n          </>\n        )}\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"max-w-2xl mx-auto mb-8\">\n            <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n              <div className=\"flex\">\n                <div className=\"ml-3\">\n                  <h3 className=\"text-sm font-medium text-red-800\">Error</h3>\n                  <div className=\"mt-2 text-sm text-red-700\">\n                    <p>{error}</p>\n                  </div>\n                  <div className=\"mt-4\">\n                    <button\n                      onClick={resetApp}\n                      className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 transition-colors\"\n                    >\n                      Try Again\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Results */}\n        {extractedData && (\n          <PDFResults data={extractedData} onExport={handleExport} />\n        )}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center text-gray-600\">\n            <p>Built with Next.js, TypeScript, and Tailwind CSS</p>\n            <p className=\"mt-2 text-sm\">Secure, fast, and privacy-focused PDF information extraction</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n\n// Feature Card Component\nfunction FeatureCard({\n  icon: Icon,\n  title,\n  description\n}: {\n  icon: any,\n  title: string,\n  description: string\n}) {\n  return (\n    <div className=\"bg-white rounded-lg p-6 shadow-sm border\">\n      <div className=\"bg-blue-100 w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4\">\n        <Icon className=\"h-6 w-6 text-blue-600\" />\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 text-sm\">{description}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,SAAS;QACT,iBAAiB;QAEjB,IAAI;YACF,MAAM,OAAO,MAAM,+HAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC;YACtD,iBAAiB;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,eAAe;QAEpB,OAAQ;YACN,KAAK;gBACH,gIAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;gBAC3B;YACF,KAAK;gBACH,gIAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;gBAC1B;YACF,KAAK;gBACH,gIAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;gBAC3B;QACJ;IACF;IAEA,MAAM,WAAW;QACf,iBAAiB;QACjB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;4BAGhC,+BACC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;oBACb,CAAC,iBAAiB,CAAC,8BAClB;;0CAEE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAA+C;;;;;;kDAK5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAM,gMAAA,CAAA,MAAG;gDACT,OAAM;gDACN,aAAY;;;;;;0DAEd,8OAAC;gDACC,MAAM,sMAAA,CAAA,SAAM;gDACZ,OAAM;gDACN,aAAY;;;;;;0DAEd,8OAAC;gDACC,MAAM,0MAAA,CAAA,WAAQ;gDACd,OAAM;gDACN,aAAY;;;;;;;;;;;;;;;;;;0CAMlB,8OAAC,iIAAA,CAAA,UAAW;gCAAC,cAAc;gCAAkB,cAAc;;;;;;;;oBAK9D,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;0DAAG;;;;;;;;;;;sDAEN,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWZ,+BACC,8OAAC,gIAAA,CAAA,UAAU;wBAAC,MAAM;wBAAe,UAAU;;;;;;;;;;;;0BAK/C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;AAEA,yBAAyB;AACzB,SAAS,YAAY,EACnB,MAAM,IAAI,EACV,KAAK,EACL,WAAW,EAKZ;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,8OAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAG5C", "debugId": null}}]}