import { NextRequest, NextResponse } from 'next/server';
import { PDFMetadata, PDFStatistics, PDFContent, ExtractedPDFData } from '@/types/pdf';
import { parsePDF } from '@/lib/pdfParser';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 });
    }

    const startTime = Date.now();

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Parse PDF using custom parser
    const pdfData = await parsePDF(buffer);

    // Extract structured data
    const extractedMetadata = extractMetadata(pdfData, file);
    const content = extractContent(pdfData);
    const statistics = calculateStatistics(pdfData, content, file, startTime);

    const result: ExtractedPDFData = {
      metadata: extractedMetadata,
      content,
      statistics,
      fileName: file.name,
      extractedAt: new Date().toISOString()
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('PDF extraction error:', error);
    return NextResponse.json(
      { error: 'Failed to extract PDF data. Please ensure the file is a valid PDF.' },
      { status: 500 }
    );
  }
}

function extractMetadata(pdfData: any, file: File): PDFMetadata {
  const info = pdfData.info || {};

  return {
    title: info.Title || 'Unknown',
    author: info.Author || 'Unknown',
    subject: info.Subject || 'Not specified',
    creator: info.Creator || 'Unknown',
    producer: info.Producer || 'Unknown',
    creationDate: info.CreationDate ? new Date(info.CreationDate).toLocaleDateString() : 'Unknown',
    modificationDate: info.ModDate ? new Date(info.ModDate).toLocaleDateString() : 'Unknown',
    keywords: info.Keywords || 'None',
    pages: pdfData.numpages || 0
  };
}

function extractContent(pdfData: any): PDFContent {
  const fullText = pdfData.text || '';
  const lines = fullText.split('\n').filter(line => line.trim());

  // Simple structure extraction
  const headings = lines.filter(line =>
    line.length < 100 &&
    (line.match(/^[A-Z\s]+$/) || line.match(/^\d+\.?\s+[A-Z]/))
  );

  const paragraphs = lines.filter(line =>
    line.length > 50 &&
    !headings.includes(line)
  );

  // Extract potential tables (lines with multiple spaces or tabs)
  const tables = lines.filter(line =>
    line.includes('\t') || line.match(/\s{3,}/)
  );

  // Extract lists (lines starting with bullets or numbers)
  const lists = lines.filter(line =>
    line.match(/^[\s]*[-•*]\s/) || line.match(/^[\s]*\d+[\.)]\s/)
  );

  // Split text by pages (approximate)
  const pageTexts = splitTextByPages(fullText, pdfData.numpages);

  return {
    fullText,
    pageTexts,
    structure: {
      headings: headings.slice(0, 20), // Limit to first 20
      paragraphs: paragraphs.slice(0, 10), // Limit to first 10
      tables: tables.slice(0, 10),
      lists: lists.slice(0, 15)
    }
  };
}

function splitTextByPages(text: string, numPages: number): string[] {
  if (numPages <= 1) return [text];

  const lines = text.split('\n');
  const linesPerPage = Math.ceil(lines.length / numPages);
  const pages: string[] = [];

  for (let i = 0; i < numPages; i++) {
    const start = i * linesPerPage;
    const end = Math.min((i + 1) * linesPerPage, lines.length);
    pages.push(lines.slice(start, end).join('\n'));
  }

  return pages;
}

function calculateStatistics(
  pdfData: any,
  content: PDFContent,
  file: File,
  startTime: number
): PDFStatistics {
  const totalCharacters = content.fullText.length;
  const totalWords = content.fullText.split(/\s+/).filter(word => word.length > 0).length;
  const totalLines = content.fullText.split('\n').length;
  const totalPages = pdfData.numpages || 1;
  const processingTime = Date.now() - startTime;

  return {
    totalPages,
    totalCharacters,
    totalWords,
    totalLines,
    averageWordsPerPage: Math.round(totalWords / totalPages),
    fileSize: formatFileSize(file.size),
    processingTime
  };
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
