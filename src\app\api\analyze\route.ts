import { NextRequest, NextResponse } from 'next/server';
import * as pdfParse from 'pdf-parse';

export async function POST(req: NextRequest) {
  try {
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    const pdfData = await pdfParse(buffer);

    // Extract sections from the PDF text
    const sections = extractSections(pdfData.text);

    return NextResponse.json({ sections });
  } catch (error) {
    console.error('Error processing PDF:', error);
    return NextResponse.json(
      { error: 'Error processing PDF file' },
      { status: 500 }
    );
  }
}

function extractSections(text: string) {
  const sections = {
    limitOfLiability: extractSection(text, 'LIMIT OF LIABILITY'),
    annualPremium: extractSection(text, 'ANNUAL PREMIUM'),
    deductible: extractSection(text, 'DEDUCTIBLE'),
    retroactiveDate: extractSection(text, 'RETROACTIVE DATE'),
    territory: extractSection(text, 'TERRITORY'),
    jurisdiction: extractSection(text, 'JURISDICTION'),
    wording: extractSection(text, 'WORDING'),
    broadCoverage: extractListSection(text, 'BROAD COVERAGE', 'MAJOR EXCLUSIONS'),
    majorExclusions: extractListSection(text, 'MAJOR EXCLUSIONS'),
  };

  return sections;
}

function extractSection(text: string, sectionName: string, endSection?: string): string {
  const startIndex = text.indexOf(sectionName);
  if (startIndex === -1) return '';

  let endIndex = text.length;
  if (endSection) {
    const nextSectionIndex = text.indexOf(endSection, startIndex);
    if (nextSectionIndex !== -1) {
      endIndex = nextSectionIndex;
    }
  }

  const sectionText = text
    .slice(startIndex + sectionName.length, endIndex)
    .split('\n')[0]
    .trim();

  return sectionText;
}

function extractListSection(text: string, sectionName: string, endSection?: string): string[] {
  const startIndex = text.indexOf(sectionName);
  if (startIndex === -1) return [];

  let endIndex = text.length;
  if (endSection) {
    const nextSectionIndex = text.indexOf(endSection, startIndex);
    if (nextSectionIndex !== -1) {
      endIndex = nextSectionIndex;
    }
  }

  const sectionText = text
    .slice(startIndex + sectionName.length, endIndex)
    .trim()
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.includes(sectionName));

  return sectionText;
}
