'use client';

import { DragAndDrop } from '@/components/DragAndDrop';
import { QuoteComparison } from '@/components/QuoteComparison';
import { useState } from 'react';

export default function Home() {
  const [quote1Data, setQuote1Data] = useState<any>(null);
  const [quote2Data, setQuote2Data] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const handleQuote1Upload = async (file: File) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await uploadAndAnalyze(file);
      setQuote1Data(data);
    } catch (err) {
      setError('Failed to analyze Quote 1. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuote2Upload = async (file: File) => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await uploadAndAnalyze(file);
      setQuote2Data(data);
    } catch (err) {
      setError('Failed to analyze Quote 2. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const uploadAndAnalyze = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/analyze', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to analyze PDF');
    }

    return await response.json();
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <header className="mb-12 text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Quote Comparison Report Tool
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Upload two insurance quotes to compare their details
          </p>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Quote 1
            </h2>
            <DragAndDrop
              onFileUpload={handleQuote1Upload}
              isLoading={isLoading}
            />
          </div>
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Quote 2
            </h2>
            <DragAndDrop
              onFileUpload={handleQuote2Upload}
              isLoading={isLoading}
            />
          </div>
        </div>

        {error && (
          <div className="mt-8 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-600 dark:text-red-400">{error}</p>
          </div>
        )}

        {(quote1Data || quote2Data) && (
          <div className="mt-12">
            <QuoteComparison quote1={quote1Data} quote2={quote2Data} />
          </div>
        )}
      </div>
    </div>
  );
}
