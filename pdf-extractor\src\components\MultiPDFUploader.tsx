'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, AlertCircle, X, Plus, GitCompare } from 'lucide-react';

interface UploadedFile {
  id: string;
  file: File;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
}

interface MultiPDFUploaderProps {
  onFilesUpload: (files: File[]) => void;
  onCompare: () => void;
  isProcessing: boolean;
  uploadedFiles: UploadedFile[];
  onRemoveFile: (id: string) => void;
}

export default function MultiPDFUploader({ 
  onFilesUpload, 
  onCompare, 
  isProcessing, 
  uploadedFiles,
  onRemoveFile 
}: MultiPDFUploaderProps) {
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setError(null);
    
    if (rejectedFiles.length > 0) {
      setError('Please upload valid PDF files only.');
      return;
    }

    // Check file sizes
    const oversizedFiles = acceptedFiles.filter(file => file.size > 10 * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      setError('Some files are larger than 10MB. Please choose smaller files.');
      return;
    }

    // Check total number of files
    if (uploadedFiles.length + acceptedFiles.length > 5) {
      setError('Maximum 5 PDF files can be compared at once.');
      return;
    }

    onFilesUpload(acceptedFiles);
  }, [onFilesUpload, uploadedFiles.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf']
    },
    multiple: true,
    disabled: isProcessing
  });

  const canCompare = uploadedFiles.length >= 2 && uploadedFiles.every(f => f.status === 'completed');

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
          ${isDragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        <div className="flex flex-col items-center space-y-4">
          {isProcessing ? (
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          ) : (
            <Upload className="h-12 w-12 text-gray-400" />
          )}
          
          <div>
            <p className="text-lg font-medium text-gray-700">
              {isProcessing 
                ? 'Processing PDFs...' 
                : isDragActive 
                  ? 'Drop the PDF files here' 
                  : 'Drag & drop PDF files here to compare'
              }
            </p>
            {!isProcessing && (
              <p className="text-sm text-gray-500 mt-1">
                or click to select files (2-5 PDFs, max 10MB each)
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <FileText className="h-6 w-6 text-gray-300" />
            <GitCompare className="h-6 w-6 text-gray-300" />
            <FileText className="h-6 w-6 text-gray-300" />
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md flex items-center space-x-2">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <span className="text-red-700 text-sm">{error}</span>
        </div>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Uploaded Files ({uploadedFiles.length}/5)
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {uploadedFiles.map((uploadedFile) => (
              <FileCard
                key={uploadedFile.id}
                uploadedFile={uploadedFile}
                onRemove={() => onRemoveFile(uploadedFile.id)}
              />
            ))}
          </div>

          {/* Compare Button */}
          <div className="flex justify-center pt-4">
            <button
              onClick={onCompare}
              disabled={!canCompare || isProcessing}
              className={`
                flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors
                ${canCompare && !isProcessing
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }
              `}
            >
              <GitCompare className="h-5 w-5" />
              <span>
                {isProcessing 
                  ? 'Processing...' 
                  : canCompare 
                    ? `Compare ${uploadedFiles.length} PDFs`
                    : `Upload ${2 - uploadedFiles.filter(f => f.status === 'completed').length} more PDF${uploadedFiles.filter(f => f.status === 'completed').length === 1 ? '' : 's'} to compare`
                }
              </span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// File Card Component
function FileCard({ uploadedFile, onRemove }: { uploadedFile: UploadedFile, onRemove: () => void }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Ready';
      case 'processing': return 'Processing...';
      case 'error': return 'Error';
      default: return 'Pending';
    }
  };

  return (
    <div className="bg-white border rounded-lg p-4 relative">
      <button
        onClick={onRemove}
        className="absolute top-2 right-2 p-1 hover:bg-gray-100 rounded-full"
      >
        <X className="h-4 w-4 text-gray-500" />
      </button>
      
      <div className="flex items-start space-x-3">
        <FileText className="h-8 w-8 text-blue-500 flex-shrink-0 mt-1" />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {uploadedFile.file.name}
          </p>
          <p className="text-xs text-gray-500">
            {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB
          </p>
          <div className="mt-2">
            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(uploadedFile.status)}`}>
              {getStatusText(uploadedFile.status)}
            </span>
          </div>
          {uploadedFile.error && (
            <p className="text-xs text-red-600 mt-1">{uploadedFile.error}</p>
          )}
        </div>
      </div>
    </div>
  );
}
