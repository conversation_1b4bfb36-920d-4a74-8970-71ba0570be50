module.exports = {

"[project]/node_modules/pdfjs-dist/build/pdf.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_pdfjs-dist_build_pdf_mjs_ceeb7271._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/pdfjs-dist/build/pdf.mjs [app-route] (ecmascript)");
    });
});
}),

};