import OpenAI from 'openai';
import { ExtractedPDFData, PDFComparison, ComparisonDifference, ComparisonAnalysis } from '@/types/pdf';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export class PDFComparator {
  static async comparePDFs(documents: ExtractedPDFData[]): Promise<PDFComparison> {
    try {
      if (documents.length < 2) {
        throw new Error('At least 2 documents are required for comparison');
      }

      const comparisonAnalysis = await this.performAIComparison(documents);
      
      return {
        id: this.generateId(),
        documents,
        differences: comparisonAnalysis.differences,
        summary: comparisonAnalysis.summary,
        recommendations: comparisonAnalysis.recommendations,
        comparedAt: new Date().toISOString(),
        comparisonType: this.detectComparisonType(documents)
      };
    } catch (error) {
      console.error('PDF comparison error:', error);
      throw error;
    }
  }

  private static async performAIComparison(documents: ExtractedPDFData[]): Promise<{
    differences: ComparisonDifference[];
    summary: string;
    recommendations: string[];
  }> {
    try {
      const prompt = this.createComparisonPrompt(documents);
      
      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "You are an expert document comparison analyst specializing in business documents, quotations, contracts, and proposals. Compare the provided documents and identify key differences, focusing on pricing, terms, vendors, timelines, and other critical business factors."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 3000,
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from OpenAI');
      }

      const analysis = JSON.parse(response);
      return this.validateComparisonAnalysis(analysis);
    } catch (error) {
      console.error('AI comparison error:', error);
      return this.createFallbackComparison(documents);
    }
  }

  private static createComparisonPrompt(documents: ExtractedPDFData[]): string {
    const documentSummaries = documents.map((doc, index) => {
      const quotationDetails = doc.aiAnalysis?.quotationDetails;
      return `
Document ${index + 1}: ${doc.fileName}
- Document Type: ${doc.aiAnalysis?.documentType || 'unknown'}
- Content Preview: ${doc.content.fullText.substring(0, 1000)}...
- Quotation Details: ${quotationDetails ? JSON.stringify(quotationDetails, null, 2) : 'None extracted'}
- Key Insights: ${doc.aiAnalysis?.keyInsights?.join('; ') || 'None'}
`;
    }).join('\n');

    return `
Compare the following ${documents.length} business documents and provide a detailed analysis in JSON format.

${documentSummaries}

Please provide analysis in this exact JSON structure:
{
  "differences": [
    {
      "field": "pricing|vendor|terms|timeline|other",
      "type": "added|removed|changed|same",
      "oldValue": "value from first document or null",
      "newValue": "value from second document or null", 
      "significance": "high|medium|low",
      "description": "Clear description of the difference"
    }
  ],
  "summary": "Overall summary of key differences between documents",
  "recommendations": [
    "Actionable recommendations based on the comparison"
  ]
}

Focus on comparing:
1. PRICING: Total costs, line item prices, discounts, taxes
2. VENDORS: Company names, contact information, credentials
3. TERMS: Payment terms, delivery dates, warranties, conditions
4. TIMELINE: Project duration, milestones, deadlines
5. SCOPE: Services/products included, specifications, quantities
6. RISKS: Potential issues, missing information, unclear terms

For each difference, assess:
- Business impact (high/medium/low significance)
- Financial implications
- Risk factors
- Recommendations for decision making

Return only valid JSON without markdown formatting.
`;
  }

  private static validateComparisonAnalysis(analysis: any): {
    differences: ComparisonDifference[];
    summary: string;
    recommendations: string[];
  } {
    return {
      differences: Array.isArray(analysis.differences) 
        ? analysis.differences.map((diff: any) => ({
            field: diff.field || 'other',
            type: diff.type || 'changed',
            oldValue: diff.oldValue,
            newValue: diff.newValue,
            significance: diff.significance || 'medium',
            description: diff.description || 'No description available'
          }))
        : [],
      summary: analysis.summary || 'Comparison completed',
      recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : []
    };
  }

  private static createFallbackComparison(documents: ExtractedPDFData[]): {
    differences: ComparisonDifference[];
    summary: string;
    recommendations: string[];
  } {
    const differences: ComparisonDifference[] = [];
    
    // Basic comparison of document types
    const docTypes = documents.map(d => d.aiAnalysis?.documentType || 'unknown');
    if (new Set(docTypes).size > 1) {
      differences.push({
        field: 'document_type',
        type: 'changed',
        oldValue: docTypes[0],
        newValue: docTypes[1],
        significance: 'medium',
        description: `Document types differ: ${docTypes.join(' vs ')}`
      });
    }

    // Basic comparison of file sizes
    const sizes = documents.map(d => d.statistics.totalCharacters);
    const sizeDiff = Math.abs(sizes[0] - sizes[1]) / Math.max(sizes[0], sizes[1]);
    if (sizeDiff > 0.2) {
      differences.push({
        field: 'content_length',
        type: 'changed',
        oldValue: sizes[0],
        newValue: sizes[1],
        significance: 'low',
        description: `Significant difference in document length: ${sizeDiff > 0.5 ? 'major' : 'moderate'} variation`
      });
    }

    return {
      differences,
      summary: `Basic comparison of ${documents.length} documents completed. AI analysis unavailable.`,
      recommendations: [
        'Review documents manually for detailed comparison',
        'Check pricing and terms carefully',
        'Verify vendor information and credentials'
      ]
    };
  }

  private static detectComparisonType(documents: ExtractedPDFData[]): 'quotation' | 'contract' | 'invoice' | 'general' {
    const types = documents.map(d => d.aiAnalysis?.documentType).filter(Boolean);
    
    if (types.every(type => type === 'quotation')) return 'quotation';
    if (types.every(type => type === 'contract')) return 'contract';
    if (types.every(type => type === 'invoice')) return 'invoice';
    
    return 'general';
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Helper method to get detailed comparison analysis
  static async getDetailedAnalysis(comparison: PDFComparison): Promise<ComparisonAnalysis> {
    const differences = comparison.differences;
    
    return {
      pricing: {
        differences: differences.filter(d => d.field.includes('pricing') || d.field.includes('cost') || d.field.includes('total')),
        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('pricing')))
      },
      terms: {
        differences: differences.filter(d => d.field.includes('terms') || d.field.includes('condition')),
        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('terms')))
      },
      vendors: {
        differences: differences.filter(d => d.field.includes('vendor') || d.field.includes('supplier')),
        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('vendor')))
      },
      timeline: {
        differences: differences.filter(d => d.field.includes('date') || d.field.includes('timeline') || d.field.includes('delivery')),
        summary: this.summarizeDifferences(differences.filter(d => d.field.includes('date')))
      },
      overall: {
        recommendation: comparison.summary,
        bestOption: this.determineBestOption(comparison),
        riskFactors: this.identifyRiskFactors(comparison)
      }
    };
  }

  private static summarizeDifferences(differences: ComparisonDifference[]): string {
    if (differences.length === 0) return 'No significant differences found';
    
    const highImpact = differences.filter(d => d.significance === 'high').length;
    const mediumImpact = differences.filter(d => d.significance === 'medium').length;
    
    return `Found ${differences.length} difference(s): ${highImpact} high impact, ${mediumImpact} medium impact`;
  }

  private static determineBestOption(comparison: PDFComparison): string | undefined {
    // Simple logic to determine best option based on differences
    const highImpactDiffs = comparison.differences.filter(d => d.significance === 'high');
    if (highImpactDiffs.length === 0) {
      return 'Options are comparable - review based on your priorities';
    }
    
    return 'Manual review recommended due to significant differences';
  }

  private static identifyRiskFactors(comparison: PDFComparison): string[] {
    const risks: string[] = [];
    
    const highImpactDiffs = comparison.differences.filter(d => d.significance === 'high');
    if (highImpactDiffs.length > 0) {
      risks.push(`${highImpactDiffs.length} high-impact differences require attention`);
    }
    
    const pricingDiffs = comparison.differences.filter(d => d.field.includes('pricing'));
    if (pricingDiffs.length > 0) {
      risks.push('Pricing variations detected - verify total costs');
    }
    
    return risks;
  }
}
