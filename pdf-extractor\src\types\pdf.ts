export interface PDFMetadata {
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: string;
  modificationDate?: string;
  keywords?: string;
  pages?: number;
}

export interface PDFStatistics {
  totalPages: number;
  totalCharacters: number;
  totalWords: number;
  totalLines: number;
  averageWordsPerPage: number;
  fileSize: string;
  processingTime: number;
}

export interface PDFContent {
  fullText: string;
  pageTexts: string[];
  structure: {
    headings: string[];
    paragraphs: string[];
    tables: string[];
    lists: string[];
  };
}

export interface QuotationDetails {
  quotationNumber?: string;
  date?: string;
  validUntil?: string;
  vendor?: {
    name?: string;
    address?: string;
    contact?: string;
    email?: string;
    phone?: string;
  };
  customer?: {
    name?: string;
    address?: string;
    contact?: string;
    email?: string;
    phone?: string;
  };
  items?: Array<{
    description?: string;
    quantity?: number;
    unitPrice?: number;
    totalPrice?: number;
    unit?: string;
  }>;
  totals?: {
    subtotal?: number;
    tax?: number;
    discount?: number;
    total?: number;
    currency?: string;
  };
  terms?: string[];
  notes?: string;
}

export interface AIAnalysis {
  documentType: string;
  confidence: number;
  quotationDetails?: QuotationDetails;
  summary: string;
  keyInsights: string[];
  recommendations?: string[];
  extractedEntities: {
    dates: string[];
    amounts: string[];
    companies: string[];
    contacts: string[];
  };
}

export interface ExtractedPDFData {
  id: string;
  metadata: PDFMetadata;
  content: PDFContent;
  statistics: PDFStatistics;
  aiAnalysis?: AIAnalysis;
  fileName: string;
  extractedAt: string;
}

export interface ComparisonDifference {
  field: string;
  type: 'added' | 'removed' | 'changed' | 'same';
  oldValue?: any;
  newValue?: any;
  significance: 'high' | 'medium' | 'low';
  description: string;
}

export interface PDFComparison {
  id: string;
  documents: ExtractedPDFData[];
  differences: ComparisonDifference[];
  summary: string;
  recommendations: string[];
  comparedAt: string;
  comparisonType: 'quotation' | 'contract' | 'invoice' | 'general';
}

export interface ComparisonAnalysis {
  pricing: {
    differences: ComparisonDifference[];
    summary: string;
  };
  terms: {
    differences: ComparisonDifference[];
    summary: string;
  };
  vendors: {
    differences: ComparisonDifference[];
    summary: string;
  };
  timeline: {
    differences: ComparisonDifference[];
    summary: string;
  };
  overall: {
    recommendation: string;
    bestOption?: string;
    riskFactors: string[];
  };
}
