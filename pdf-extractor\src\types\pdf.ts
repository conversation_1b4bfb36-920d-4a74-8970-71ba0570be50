export interface PDFMetadata {
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: string;
  modificationDate?: string;
  keywords?: string;
  pages?: number;
}

export interface PDFStatistics {
  totalPages: number;
  totalCharacters: number;
  totalWords: number;
  totalLines: number;
  averageWordsPerPage: number;
  fileSize: string;
  processingTime: number;
}

export interface PDFContent {
  fullText: string;
  pageTexts: string[];
  structure: {
    headings: string[];
    paragraphs: string[];
    tables: string[];
    lists: string[];
  };
}

export interface QuotationDetails {
  quotationNumber?: string;
  date?: string;
  validUntil?: string;
  vendor?: {
    name?: string;
    address?: string;
    contact?: string;
    email?: string;
    phone?: string;
  };
  customer?: {
    name?: string;
    address?: string;
    contact?: string;
    email?: string;
    phone?: string;
  };
  items?: Array<{
    description?: string;
    quantity?: number;
    unitPrice?: number;
    totalPrice?: number;
    unit?: string;
  }>;
  totals?: {
    subtotal?: number;
    tax?: number;
    discount?: number;
    total?: number;
    currency?: string;
  };
  terms?: string[];
  notes?: string;
}

export interface AIAnalysis {
  documentType: string;
  confidence: number;
  quotationDetails?: QuotationDetails;
  summary: string;
  keyInsights: string[];
  recommendations?: string[];
  extractedEntities: {
    dates: string[];
    amounts: string[];
    companies: string[];
    contacts: string[];
  };
}

export interface ExtractedPDFData {
  id: string;
  metadata: PDFMetadata;
  content: PDFContent;
  statistics: PDFStatistics;
  aiAnalysis?: AIAnalysis;
  fileName: string;
  extractedAt: string;
}

export interface ComparisonDifference {
  field: string;
  type: 'added' | 'removed' | 'changed' | 'same';
  oldValue?: any;
  newValue?: any;
  significance: 'high' | 'medium' | 'low';
  description: string;
}

export interface PDFComparison {
  id: string;
  documents: ExtractedPDFData[];
  differences: ComparisonDifference[];
  summary: string;
  recommendations: string[];
  comparedAt: string;
  comparisonType: 'quotation' | 'contract' | 'invoice' | 'general';
}

export interface QuotationScore {
  documentId: string;
  fileName: string;
  totalScore: number; // 0-100
  scores: {
    pricing: number; // 0-100 (lower price = higher score)
    terms: number; // 0-100 (better terms = higher score)
    vendor: number; // 0-100 (vendor reputation/reliability)
    timeline: number; // 0-100 (faster delivery = higher score)
    completeness: number; // 0-100 (more detailed = higher score)
  };
  strengths: string[];
  weaknesses: string[];
  recommendation: 'best' | 'good' | 'acceptable' | 'poor';
}

export interface SideBySideComparison {
  documents: ExtractedPDFData[];
  scores: QuotationScore[];
  bestChoice: {
    documentId: string;
    reason: string;
    confidence: number;
  };
  comparisonMatrix: {
    field: string;
    category: 'pricing' | 'terms' | 'vendor' | 'timeline' | 'other';
    values: Array<{
      documentId: string;
      value: string;
      isBest: boolean;
      isWorst: boolean;
      score: number;
    }>;
  }[];
}

export interface ComparisonAnalysis {
  pricing: {
    differences: ComparisonDifference[];
    summary: string;
  };
  terms: {
    differences: ComparisonDifference[];
    summary: string;
  };
  vendors: {
    differences: ComparisonDifference[];
    summary: string;
  };
  timeline: {
    differences: ComparisonDifference[];
    summary: string;
  };
  overall: {
    recommendation: string;
    bestOption?: string;
    riskFactors: string[];
  };
  sideBySide?: SideBySideComparison;
}
