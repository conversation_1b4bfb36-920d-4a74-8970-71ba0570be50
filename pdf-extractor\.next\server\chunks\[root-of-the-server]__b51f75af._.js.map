{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/lib/pdfParser.ts"], "sourcesContent": ["// Custom PDF parser wrapper to handle pdf-parse import issues\nexport async function parsePDF(buffer: Buffer) {\n  try {\n    // Create a mock test file to prevent the ENOENT error\n    const fs = require('fs');\n    const path = require('path');\n    \n    // Create test directory if it doesn't exist\n    const testDir = path.join(process.cwd(), 'test', 'data');\n    const testFile = path.join(testDir, '05-versions-space.pdf');\n    \n    if (!fs.existsSync(testDir)) {\n      fs.mkdirSync(testDir, { recursive: true });\n    }\n    \n    if (!fs.existsSync(testFile)) {\n      // Create a minimal PDF file to satisfy the test\n      const minimalPDF = Buffer.from([\n        0x25, 0x50, 0x44, 0x46, 0x2D, 0x31, 0x2E, 0x34, // %PDF-1.4\n        0x0A, 0x25, 0xE2, 0xE3, 0xCF, 0xD3, 0x0A, // Binary comment\n        0x31, 0x20, 0x30, 0x20, 0x6F, 0x62, 0x6A, 0x0A, // 1 0 obj\n        0x3C, 0x3C, 0x2F, 0x54, 0x79, 0x70, 0x65, 0x2F, 0x43, 0x61, 0x74, 0x61, 0x6C, 0x6F, 0x67, 0x2F, 0x50, 0x61, 0x67, 0x65, 0x73, 0x20, 0x32, 0x20, 0x30, 0x20, 0x52, 0x3E, 0x3E, 0x0A, // <</Type/Catalog/Pages 2 0 R>>\n        0x65, 0x6E, 0x64, 0x6F, 0x62, 0x6A, 0x0A, // endobj\n        0x32, 0x20, 0x30, 0x20, 0x6F, 0x62, 0x6A, 0x0A, // 2 0 obj\n        0x3C, 0x3C, 0x2F, 0x54, 0x79, 0x70, 0x65, 0x2F, 0x50, 0x61, 0x67, 0x65, 0x73, 0x2F, 0x4B, 0x69, 0x64, 0x73, 0x5B, 0x33, 0x20, 0x30, 0x20, 0x52, 0x5D, 0x2F, 0x43, 0x6F, 0x75, 0x6E, 0x74, 0x20, 0x31, 0x3E, 0x3E, 0x0A, // <</Type/Pages/Kids[3 0 R]/Count 1>>\n        0x65, 0x6E, 0x64, 0x6F, 0x62, 0x6A, 0x0A, // endobj\n        0x33, 0x20, 0x30, 0x20, 0x6F, 0x62, 0x6A, 0x0A, // 3 0 obj\n        0x3C, 0x3C, 0x2F, 0x54, 0x79, 0x70, 0x65, 0x2F, 0x50, 0x61, 0x67, 0x65, 0x2F, 0x50, 0x61, 0x72, 0x65, 0x6E, 0x74, 0x20, 0x32, 0x20, 0x30, 0x20, 0x52, 0x2F, 0x4D, 0x65, 0x64, 0x69, 0x61, 0x42, 0x6F, 0x78, 0x5B, 0x30, 0x20, 0x30, 0x20, 0x36, 0x31, 0x32, 0x20, 0x37, 0x39, 0x32, 0x5D, 0x3E, 0x3E, 0x0A, // <</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>\n        0x65, 0x6E, 0x64, 0x6F, 0x62, 0x6A, 0x0A, // endobj\n        0x78, 0x72, 0x65, 0x66, 0x0A, // xref\n        0x30, 0x20, 0x34, 0x0A, // 0 4\n        0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x20, 0x36, 0x35, 0x35, 0x33, 0x35, 0x20, 0x66, 0x20, 0x0A, // 0000000000 65535 f \n        0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x39, 0x20, 0x30, 0x30, 0x30, 0x30, 0x30, 0x20, 0x6E, 0x20, 0x0A, // 0000000009 00000 n \n        0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x37, 0x34, 0x20, 0x30, 0x30, 0x30, 0x30, 0x30, 0x20, 0x6E, 0x20, 0x0A, // 0000000074 00000 n \n        0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x31, 0x32, 0x30, 0x20, 0x30, 0x30, 0x30, 0x30, 0x30, 0x20, 0x6E, 0x20, 0x0A, // 0000000120 00000 n \n        0x74, 0x72, 0x61, 0x69, 0x6C, 0x65, 0x72, 0x0A, // trailer\n        0x3C, 0x3C, 0x2F, 0x53, 0x69, 0x7A, 0x65, 0x20, 0x34, 0x2F, 0x52, 0x6F, 0x6F, 0x74, 0x20, 0x31, 0x20, 0x30, 0x20, 0x52, 0x3E, 0x3E, 0x0A, // <</Size 4/Root 1 0 R>>\n        0x73, 0x74, 0x61, 0x72, 0x74, 0x78, 0x72, 0x65, 0x66, 0x0A, // startxref\n        0x31, 0x38, 0x31, 0x0A, // 181\n        0x25, 0x25, 0x45, 0x4F, 0x46 // %%EOF\n      ]);\n      fs.writeFileSync(testFile, minimalPDF);\n    }\n    \n    // Now import pdf-parse\n    const pdfParse = require('pdf-parse');\n    \n    // Parse the actual PDF\n    return await pdfParse(buffer);\n  } catch (error) {\n    console.error('PDF parsing error:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AACvD,eAAe,SAAS,MAAc;IAC3C,IAAI;QACF,sDAAsD;QACtD,MAAM;QACN,MAAM;QAEN,4CAA4C;QAC5C,MAAM,UAAU,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;QACjD,MAAM,WAAW,KAAK,IAAI,CAAC,SAAS;QAEpC,IAAI,CAAC,GAAG,UAAU,CAAC,UAAU;YAC3B,GAAG,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;QAEA,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW;YAC5B,gDAAgD;YAChD,MAAM,aAAa,OAAO,IAAI,CAAC;gBAC7B;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC1C;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACpC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC1C;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC9K;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACpC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC1C;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAClN;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACpC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC1C;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACtS;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACpC;gBAAM;gBAAM;gBAAM;gBAAM;gBACxB;gBAAM;gBAAM;gBAAM;gBAClB;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAClH;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAClH;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAClH;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAClH;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAC1C;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACpI;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBACtD;gBAAM;gBAAM;gBAAM;gBAClB;gBAAM;gBAAM;gBAAM;gBAAM,KAAK,QAAQ;aACtC;YACD,GAAG,aAAa,CAAC,UAAU;QAC7B;QAEA,uBAAuB;QACvB,MAAM;QAEN,uBAAuB;QACvB,OAAO,MAAM,SAAS;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/next/pdf-extractor/src/app/api/extract-pdf/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { PDFMetadata, PDFStatistics, PDFContent, ExtractedPDFData } from '@/types/pdf';\nimport { parsePDF } from '@/lib/pdfParser';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    if (file.type !== 'application/pdf') {\n      return NextResponse.json({ error: 'File must be a PDF' }, { status: 400 });\n    }\n\n    if (file.size > 10 * 1024 * 1024) { // 10MB limit\n      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 });\n    }\n\n    const startTime = Date.now();\n\n    // Convert file to buffer\n    const arrayBuffer = await file.arrayBuffer();\n    const buffer = Buffer.from(arrayBuffer);\n\n    // Parse PDF using custom parser\n    const pdfData = await parsePDF(buffer);\n\n    // Extract structured data\n    const extractedMetadata = extractMetadata(pdfData, file);\n    const content = extractContent(pdfData);\n    const statistics = calculateStatistics(pdfData, content, file, startTime);\n\n    const result: ExtractedPDFData = {\n      metadata: extractedMetadata,\n      content,\n      statistics,\n      fileName: file.name,\n      extractedAt: new Date().toISOString()\n    };\n\n    return NextResponse.json(result);\n  } catch (error) {\n    console.error('PDF extraction error:', error);\n    return NextResponse.json(\n      { error: 'Failed to extract PDF data. Please ensure the file is a valid PDF.' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction extractMetadata(pdfData: any, file: File): PDFMetadata {\n  const info = pdfData.info || {};\n\n  return {\n    title: info.Title || 'Unknown',\n    author: info.Author || 'Unknown',\n    subject: info.Subject || 'Not specified',\n    creator: info.Creator || 'Unknown',\n    producer: info.Producer || 'Unknown',\n    creationDate: info.CreationDate ? new Date(info.CreationDate).toLocaleDateString() : 'Unknown',\n    modificationDate: info.ModDate ? new Date(info.ModDate).toLocaleDateString() : 'Unknown',\n    keywords: info.Keywords || 'None',\n    pages: pdfData.numpages || 0\n  };\n}\n\nfunction extractContent(pdfData: any): PDFContent {\n  const fullText = pdfData.text || '';\n  const lines = fullText.split('\\n').filter(line => line.trim());\n\n  // Simple structure extraction\n  const headings = lines.filter(line =>\n    line.length < 100 &&\n    (line.match(/^[A-Z\\s]+$/) || line.match(/^\\d+\\.?\\s+[A-Z]/))\n  );\n\n  const paragraphs = lines.filter(line =>\n    line.length > 50 &&\n    !headings.includes(line)\n  );\n\n  // Extract potential tables (lines with multiple spaces or tabs)\n  const tables = lines.filter(line =>\n    line.includes('\\t') || line.match(/\\s{3,}/)\n  );\n\n  // Extract lists (lines starting with bullets or numbers)\n  const lists = lines.filter(line =>\n    line.match(/^[\\s]*[-•*]\\s/) || line.match(/^[\\s]*\\d+[\\.)]\\s/)\n  );\n\n  // Split text by pages (approximate)\n  const pageTexts = splitTextByPages(fullText, pdfData.numpages);\n\n  return {\n    fullText,\n    pageTexts,\n    structure: {\n      headings: headings.slice(0, 20), // Limit to first 20\n      paragraphs: paragraphs.slice(0, 10), // Limit to first 10\n      tables: tables.slice(0, 10),\n      lists: lists.slice(0, 15)\n    }\n  };\n}\n\nfunction splitTextByPages(text: string, numPages: number): string[] {\n  if (numPages <= 1) return [text];\n\n  const lines = text.split('\\n');\n  const linesPerPage = Math.ceil(lines.length / numPages);\n  const pages: string[] = [];\n\n  for (let i = 0; i < numPages; i++) {\n    const start = i * linesPerPage;\n    const end = Math.min((i + 1) * linesPerPage, lines.length);\n    pages.push(lines.slice(start, end).join('\\n'));\n  }\n\n  return pages;\n}\n\nfunction calculateStatistics(\n  pdfData: any,\n  content: PDFContent,\n  file: File,\n  startTime: number\n): PDFStatistics {\n  const totalCharacters = content.fullText.length;\n  const totalWords = content.fullText.split(/\\s+/).filter(word => word.length > 0).length;\n  const totalLines = content.fullText.split('\\n').length;\n  const totalPages = pdfData.numpages || 1;\n  const processingTime = Date.now() - startTime;\n\n  return {\n    totalPages,\n    totalCharacters,\n    totalWords,\n    totalLines,\n    averageWordsPerPage: Math.round(totalWords / totalPages),\n    fileSize: formatFileSize(file.size),\n    processingTime\n  };\n}\n\nfunction formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmC,GAAG;gBAAE,QAAQ;YAAI;QACxF;QAEA,MAAM,YAAY,KAAK,GAAG;QAE1B,yBAAyB;QACzB,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,gCAAgC;QAChC,MAAM,UAAU,MAAM,CAAA,GAAA,yHAAA,CAAA,WAAQ,AAAD,EAAE;QAE/B,0BAA0B;QAC1B,MAAM,oBAAoB,gBAAgB,SAAS;QACnD,MAAM,UAAU,eAAe;QAC/B,MAAM,aAAa,oBAAoB,SAAS,SAAS,MAAM;QAE/D,MAAM,SAA2B;YAC/B,UAAU;YACV;YACA;YACA,UAAU,KAAK,IAAI;YACnB,aAAa,IAAI,OAAO,WAAW;QACrC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqE,GAC9E;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS,gBAAgB,OAAY,EAAE,IAAU;IAC/C,MAAM,OAAO,QAAQ,IAAI,IAAI,CAAC;IAE9B,OAAO;QACL,OAAO,KAAK,KAAK,IAAI;QACrB,QAAQ,KAAK,MAAM,IAAI;QACvB,SAAS,KAAK,OAAO,IAAI;QACzB,SAAS,KAAK,OAAO,IAAI;QACzB,UAAU,KAAK,QAAQ,IAAI;QAC3B,cAAc,KAAK,YAAY,GAAG,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,KAAK;QACrF,kBAAkB,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB,KAAK;QAC/E,UAAU,KAAK,QAAQ,IAAI;QAC3B,OAAO,QAAQ,QAAQ,IAAI;IAC7B;AACF;AAEA,SAAS,eAAe,OAAY;IAClC,MAAM,WAAW,QAAQ,IAAI,IAAI;IACjC,MAAM,QAAQ,SAAS,KAAK,CAAC,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI;IAE3D,8BAA8B;IAC9B,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAC5B,KAAK,MAAM,GAAG,OACd,CAAC,KAAK,KAAK,CAAC,iBAAiB,KAAK,KAAK,CAAC,kBAAkB;IAG5D,MAAM,aAAa,MAAM,MAAM,CAAC,CAAA,OAC9B,KAAK,MAAM,GAAG,MACd,CAAC,SAAS,QAAQ,CAAC;IAGrB,gEAAgE;IAChE,MAAM,SAAS,MAAM,MAAM,CAAC,CAAA,OAC1B,KAAK,QAAQ,CAAC,SAAS,KAAK,KAAK,CAAC;IAGpC,yDAAyD;IACzD,MAAM,QAAQ,MAAM,MAAM,CAAC,CAAA,OACzB,KAAK,KAAK,CAAC,oBAAoB,KAAK,KAAK,CAAC;IAG5C,oCAAoC;IACpC,MAAM,YAAY,iBAAiB,UAAU,QAAQ,QAAQ;IAE7D,OAAO;QACL;QACA;QACA,WAAW;YACT,UAAU,SAAS,KAAK,CAAC,GAAG;YAC5B,YAAY,WAAW,KAAK,CAAC,GAAG;YAChC,QAAQ,OAAO,KAAK,CAAC,GAAG;YACxB,OAAO,MAAM,KAAK,CAAC,GAAG;QACxB;IACF;AACF;AAEA,SAAS,iBAAiB,IAAY,EAAE,QAAgB;IACtD,IAAI,YAAY,GAAG,OAAO;QAAC;KAAK;IAEhC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,eAAe,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;IAC9C,MAAM,QAAkB,EAAE;IAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;QACjC,MAAM,QAAQ,IAAI;QAClB,MAAM,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,cAAc,MAAM,MAAM;QACzD,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC;IAC1C;IAEA,OAAO;AACT;AAEA,SAAS,oBACP,OAAY,EACZ,OAAmB,EACnB,IAAU,EACV,SAAiB;IAEjB,MAAM,kBAAkB,QAAQ,QAAQ,CAAC,MAAM;IAC/C,MAAM,aAAa,QAAQ,QAAQ,CAAC,KAAK,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAG,MAAM;IACvF,MAAM,aAAa,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,MAAM;IACtD,MAAM,aAAa,QAAQ,QAAQ,IAAI;IACvC,MAAM,iBAAiB,KAAK,GAAG,KAAK;IAEpC,OAAO;QACL;QACA;QACA;QACA;QACA,qBAAqB,KAAK,KAAK,CAAC,aAAa;QAC7C,UAAU,eAAe,KAAK,IAAI;QAClC;IACF;AACF;AAEA,SAAS,eAAe,KAAa;IACnC,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE", "debugId": null}}]}