const CHUNK_PUBLIC_PATH = "server/app/api/extract-pdf/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_5a10e5a2._.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_100_build_pdf_worker_52c9fc57.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_100_build_pdf_71c3c8b3.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_88_build_pdf_worker_ea871991.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_10_88_build_pdf_36252b84.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_9_426_build_pdf_worker_c153a02e.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v1_9_426_build_pdf_fe79cad9.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v2_0_550_build_pdf_worker_4a586fdb.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf_js_v2_0_550_build_pdf_8d84ce2e.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_lib_pdf-parse_30edfd6e.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_index_f716ad8d.js");
runtime.loadChunk("server/chunks/node_modules_openai_fbab7b8a._.js");
runtime.loadChunk("server/chunks/node_modules_node-ensure_index_7f950531.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__3da2bc54._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/extract-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
