const CHUNK_PUBLIC_PATH = "server/app/api/extract-pdf/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_pdf-parse_index_75b693cc.js");
runtime.loadChunk("server/chunks/node_modules_next_5a10e5a2._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__f254d15c._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/extract-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
