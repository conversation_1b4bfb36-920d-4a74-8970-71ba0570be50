import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test if pdfjs-dist can be imported without issues
    const pdfjsLib = await import('pdfjs-dist');

    return NextResponse.json({
      status: 'success',
      message: 'PDF.js library loaded successfully',
      version: pdfjsLib.version || 'unknown'
    });
  } catch (error) {
    console.error('PDF.js test error:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to load PDF.js library',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
