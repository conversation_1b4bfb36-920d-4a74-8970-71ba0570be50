'use client';

import { useMemo } from 'react';

interface QuoteComparisonProps {
  quote1: any;
  quote2: any;
}

export function QuoteComparison({ quote1, quote2 }: QuoteComparisonProps) {
  const comparison = useMemo(() => {
    return {
      mainFields: compareMainFields(quote1.sections, quote2.sections),
      broadCoverage: compareLists(
        quote1.sections.broadCoverage,
        quote2.sections.broadCoverage
      ),
      majorExclusions: compareLists(
        quote1.sections.majorExclusions,
        quote2.sections.majorExclusions
      ),
    };
  }, [quote1, quote2]);

  const totalFields = useMemo(() => {
    return (
      Object.keys(comparison.mainFields).length +
      comparison.broadCoverage.all.length +
      comparison.majorExclusions.all.length
    );
  }, [comparison]);

  const matchingFields = useMemo(() => {
    return (
      Object.values(comparison.mainFields).filter((field) => field.matches).length +
      comparison.broadCoverage.common.length +
      comparison.majorExclusions.common.length
    );
  }, [comparison]);

  const matchPercentage = (matchingFields / totalFields) * 100;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-3xl font-bold mb-6">Comparison Results</h2>

      {/* Main Fields Comparison */}
      <section className="mb-8">
        <h3 className="text-2xl font-semibold mb-4">Main Fields</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(comparison.mainFields).map(([field, comparison]) => (
            <div
              key={field}
              className={`p-4 rounded-lg ${
                comparison.matches ? 'bg-green-50' : 'bg-yellow-50'
              }`}
            >
              <h4 className="font-medium mb-2">{field}</h4>
              <div className="text-sm">
                <p>Quote 1: {comparison.value1}</p>
                <p>Quote 2: {comparison.value2}</p>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Broad Coverage Comparison */}
      <section className="mb-8">
        <h3 className="text-2xl font-semibold mb-4">Broad Coverage</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium mb-2">Only in Quote 1</h4>
            <ul className="list-disc pl-5">
              {comparison.broadCoverage.onlyIn1.map((item) => (
                <li key={item} className="text-sm">{item}</li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Common Coverage</h4>
            <ul className="list-disc pl-5">
              {comparison.broadCoverage.common.map((item) => (
                <li key={item} className="text-sm">{item}</li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Only in Quote 2</h4>
            <ul className="list-disc pl-5">
              {comparison.broadCoverage.onlyIn2.map((item) => (
                <li key={item} className="text-sm">{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* Major Exclusions Comparison */}
      <section className="mb-8">
        <h3 className="text-2xl font-semibold mb-4">Major Exclusions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium mb-2">Only in Quote 1</h4>
            <ul className="list-disc pl-5">
              {comparison.majorExclusions.onlyIn1.map((item) => (
                <li key={item} className="text-sm">{item}</li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Common Exclusions</h4>
            <ul className="list-disc pl-5">
              {comparison.majorExclusions.common.map((item) => (
                <li key={item} className="text-sm">{item}</li>
              ))}
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Only in Quote 2</h4>
            <ul className="list-disc pl-5">
              {comparison.majorExclusions.onlyIn2.map((item) => (
                <li key={item} className="text-sm">{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </section>

      {/* Summary */}
      <section className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-2xl font-semibold mb-4">Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-gray-600">Total Fields</p>
            <p className="text-3xl font-bold">{totalFields}</p>
          </div>
          <div>
            <p className="text-gray-600">Matching Fields</p>
            <p className="text-3xl font-bold">{matchingFields}</p>
          </div>
          <div>
            <p className="text-gray-600">Match Percentage</p>
            <p className="text-3xl font-bold">{matchPercentage.toFixed(1)}%</p>
          </div>
        </div>
      </section>
    </div>
  );
}

function compareMainFields(sections1: any, sections2: any) {
  const mainFields = [
    'limitOfLiability',
    'annualPremium',
    'deductible',
    'retroactiveDate',
    'territory',
    'jurisdiction',
    'wording',
  ];

  const result: Record<string, { value1: string; value2: string; matches: boolean }> = {};

  for (const field of mainFields) {
    const value1 = sections1[field] || 'Not specified';
    const value2 = sections2[field] || 'Not specified';
    result[field] = {
      value1,
      value2,
      matches: value1 === value2,
    };
  }

  return result;
}

function compareLists(list1: string[], list2: string[]) {
  const set1 = new Set(list1);
  const set2 = new Set(list2);
  const all = new Set([...list1, ...list2]);

  const common = [...set1].filter(x => set2.has(x));
  const onlyIn1 = [...set1].filter(x => !set2.has(x));
  const onlyIn2 = [...set2].filter(x => !set1.has(x));

  return {
    all: [...all],
    common,
    onlyIn1,
    onlyIn2,
  };
}
