module.exports = {

"[project]/.next-internal/server/app/api/extract-pdf/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[project]/src/lib/pdfParser.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Custom PDF parser wrapper to handle pdf-parse import issues
__turbopack_context__.s({
    "parsePDF": ()=>parsePDF
});
async function parsePDF(buffer) {
    try {
        // Create a mock test file to prevent the ENOENT error
        const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
        const path = __turbopack_context__.r("[externals]/path [external] (path, cjs)");
        // Create test directory if it doesn't exist
        const testDir = path.join(process.cwd(), 'test', 'data');
        const testFile = path.join(testDir, '05-versions-space.pdf');
        if (!fs.existsSync(testDir)) {
            fs.mkdirSync(testDir, {
                recursive: true
            });
        }
        if (!fs.existsSync(testFile)) {
            // Create a minimal PDF file to satisfy the test
            const minimalPDF = Buffer.from([
                0x25,
                0x50,
                0x44,
                0x46,
                0x2D,
                0x31,
                0x2E,
                0x34,
                0x0A,
                0x25,
                0xE2,
                0xE3,
                0xCF,
                0xD3,
                0x0A,
                0x31,
                0x20,
                0x30,
                0x20,
                0x6F,
                0x62,
                0x6A,
                0x0A,
                0x3C,
                0x3C,
                0x2F,
                0x54,
                0x79,
                0x70,
                0x65,
                0x2F,
                0x43,
                0x61,
                0x74,
                0x61,
                0x6C,
                0x6F,
                0x67,
                0x2F,
                0x50,
                0x61,
                0x67,
                0x65,
                0x73,
                0x20,
                0x32,
                0x20,
                0x30,
                0x20,
                0x52,
                0x3E,
                0x3E,
                0x0A,
                0x65,
                0x6E,
                0x64,
                0x6F,
                0x62,
                0x6A,
                0x0A,
                0x32,
                0x20,
                0x30,
                0x20,
                0x6F,
                0x62,
                0x6A,
                0x0A,
                0x3C,
                0x3C,
                0x2F,
                0x54,
                0x79,
                0x70,
                0x65,
                0x2F,
                0x50,
                0x61,
                0x67,
                0x65,
                0x73,
                0x2F,
                0x4B,
                0x69,
                0x64,
                0x73,
                0x5B,
                0x33,
                0x20,
                0x30,
                0x20,
                0x52,
                0x5D,
                0x2F,
                0x43,
                0x6F,
                0x75,
                0x6E,
                0x74,
                0x20,
                0x31,
                0x3E,
                0x3E,
                0x0A,
                0x65,
                0x6E,
                0x64,
                0x6F,
                0x62,
                0x6A,
                0x0A,
                0x33,
                0x20,
                0x30,
                0x20,
                0x6F,
                0x62,
                0x6A,
                0x0A,
                0x3C,
                0x3C,
                0x2F,
                0x54,
                0x79,
                0x70,
                0x65,
                0x2F,
                0x50,
                0x61,
                0x67,
                0x65,
                0x2F,
                0x50,
                0x61,
                0x72,
                0x65,
                0x6E,
                0x74,
                0x20,
                0x32,
                0x20,
                0x30,
                0x20,
                0x52,
                0x2F,
                0x4D,
                0x65,
                0x64,
                0x69,
                0x61,
                0x42,
                0x6F,
                0x78,
                0x5B,
                0x30,
                0x20,
                0x30,
                0x20,
                0x36,
                0x31,
                0x32,
                0x20,
                0x37,
                0x39,
                0x32,
                0x5D,
                0x3E,
                0x3E,
                0x0A,
                0x65,
                0x6E,
                0x64,
                0x6F,
                0x62,
                0x6A,
                0x0A,
                0x78,
                0x72,
                0x65,
                0x66,
                0x0A,
                0x30,
                0x20,
                0x34,
                0x0A,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x20,
                0x36,
                0x35,
                0x35,
                0x33,
                0x35,
                0x20,
                0x66,
                0x20,
                0x0A,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x39,
                0x20,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x20,
                0x6E,
                0x20,
                0x0A,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x37,
                0x34,
                0x20,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x20,
                0x6E,
                0x20,
                0x0A,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x31,
                0x32,
                0x30,
                0x20,
                0x30,
                0x30,
                0x30,
                0x30,
                0x30,
                0x20,
                0x6E,
                0x20,
                0x0A,
                0x74,
                0x72,
                0x61,
                0x69,
                0x6C,
                0x65,
                0x72,
                0x0A,
                0x3C,
                0x3C,
                0x2F,
                0x53,
                0x69,
                0x7A,
                0x65,
                0x20,
                0x34,
                0x2F,
                0x52,
                0x6F,
                0x6F,
                0x74,
                0x20,
                0x31,
                0x20,
                0x30,
                0x20,
                0x52,
                0x3E,
                0x3E,
                0x0A,
                0x73,
                0x74,
                0x61,
                0x72,
                0x74,
                0x78,
                0x72,
                0x65,
                0x66,
                0x0A,
                0x31,
                0x38,
                0x31,
                0x0A,
                0x25,
                0x25,
                0x45,
                0x4F,
                0x46 // %%EOF
            ]);
            fs.writeFileSync(testFile, minimalPDF);
        }
        // Now import pdf-parse
        const pdfParse = __turbopack_context__.r("[project]/node_modules/pdf-parse/index.js [app-route] (ecmascript)");
        // Parse the actual PDF
        return await pdfParse(buffer);
    } catch (error) {
        console.error('PDF parsing error:', error);
        throw error;
    }
}
}),
"[project]/src/services/aiAnalyzer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AIAnalyzer": ()=>AIAnalyzer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
class AIAnalyzer {
    static async analyzeDocument(text, fileName) {
        try {
            const prompt = this.createAnalysisPrompt(text, fileName);
            const completion = await openai.chat.completions.create({
                model: "gpt-4o-mini",
                messages: [
                    {
                        role: "system",
                        content: "You are an expert document analyzer specializing in business documents, quotations, invoices, and contracts. Analyze the provided text and extract structured information in JSON format."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                temperature: 0.1,
                max_tokens: 2000
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from OpenAI');
            }
            // Parse the JSON response
            const analysis = JSON.parse(response);
            return this.validateAndFormatAnalysis(analysis);
        } catch (error) {
            console.error('AI Analysis error:', error);
            return this.createFallbackAnalysis(text);
        }
    }
    static createAnalysisPrompt(text, fileName) {
        return `
Analyze the following document text and provide a comprehensive analysis in JSON format.

Document Name: ${fileName}
Document Text:
${text.substring(0, 4000)} ${text.length > 4000 ? '...(truncated)' : ''}

Please provide analysis in this exact JSON structure:
{
  "documentType": "quotation|invoice|contract|proposal|other",
  "confidence": 0.95,
  "quotationDetails": {
    "quotationNumber": "string or null",
    "date": "YYYY-MM-DD or null",
    "validUntil": "YYYY-MM-DD or null",
    "vendor": {
      "name": "string or null",
      "address": "string or null",
      "contact": "string or null",
      "email": "string or null",
      "phone": "string or null"
    },
    "customer": {
      "name": "string or null",
      "address": "string or null",
      "contact": "string or null",
      "email": "string or null",
      "phone": "string or null"
    },
    "items": [
      {
        "description": "string",
        "quantity": number,
        "unitPrice": number,
        "totalPrice": number,
        "unit": "string"
      }
    ],
    "totals": {
      "subtotal": number,
      "tax": number,
      "discount": number,
      "total": number,
      "currency": "USD|EUR|GBP|etc"
    },
    "terms": ["array of terms and conditions"],
    "notes": "additional notes"
  },
  "summary": "Brief summary of the document",
  "keyInsights": ["array of key insights"],
  "recommendations": ["array of recommendations"],
  "extractedEntities": {
    "dates": ["array of dates found"],
    "amounts": ["array of monetary amounts"],
    "companies": ["array of company names"],
    "contacts": ["array of contact information"]
  }
}

Focus on:
1. Identifying if this is a quotation/quote document
2. Extracting all pricing information, line items, and totals
3. Finding vendor and customer details
4. Identifying key dates (quote date, validity, delivery dates)
5. Extracting terms and conditions
6. Providing actionable insights about the quotation

Return only valid JSON without any markdown formatting or additional text.
`;
    }
    static validateAndFormatAnalysis(analysis) {
        return {
            documentType: analysis.documentType || 'unknown',
            confidence: Math.min(Math.max(analysis.confidence || 0, 0), 1),
            quotationDetails: analysis.quotationDetails || undefined,
            summary: analysis.summary || 'No summary available',
            keyInsights: Array.isArray(analysis.keyInsights) ? analysis.keyInsights : [],
            recommendations: Array.isArray(analysis.recommendations) ? analysis.recommendations : undefined,
            extractedEntities: {
                dates: Array.isArray(analysis.extractedEntities?.dates) ? analysis.extractedEntities.dates : [],
                amounts: Array.isArray(analysis.extractedEntities?.amounts) ? analysis.extractedEntities.amounts : [],
                companies: Array.isArray(analysis.extractedEntities?.companies) ? analysis.extractedEntities.companies : [],
                contacts: Array.isArray(analysis.extractedEntities?.contacts) ? analysis.extractedEntities.contacts : []
            }
        };
    }
    static createFallbackAnalysis(text) {
        // Simple fallback analysis using basic text processing
        const words = text.toLowerCase();
        const isQuotation = words.includes('quotation') || words.includes('quote') || words.includes('estimate');
        const isInvoice = words.includes('invoice') || words.includes('bill');
        const isContract = words.includes('contract') || words.includes('agreement');
        let documentType = 'other';
        if (isQuotation) documentType = 'quotation';
        else if (isInvoice) documentType = 'invoice';
        else if (isContract) documentType = 'contract';
        // Extract basic entities
        const dateRegex = /\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}|\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2}/g;
        const amountRegex = /\$[\d,]+\.?\d*|[\d,]+\.?\d*\s*(?:USD|EUR|GBP|\$)/gi;
        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
        const dates = text.match(dateRegex) || [];
        const amounts = text.match(amountRegex) || [];
        const emails = text.match(emailRegex) || [];
        return {
            documentType,
            confidence: 0.6,
            summary: `Document appears to be a ${documentType}. Basic analysis completed due to AI service unavailability.`,
            keyInsights: [
                `Document type identified as: ${documentType}`,
                `Found ${dates.length} date references`,
                `Found ${amounts.length} monetary amounts`,
                `Found ${emails.length} email addresses`
            ],
            extractedEntities: {
                dates,
                amounts,
                companies: [],
                contacts: emails
            }
        };
    }
}
}),
"[project]/src/app/api/extract-pdf/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdfParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/pdfParser.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$aiAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/aiAnalyzer.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        if (file.type !== 'application/pdf') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File must be a PDF'
            }, {
                status: 400
            });
        }
        if (file.size > 10 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File size must be less than 10MB'
            }, {
                status: 400
            });
        }
        const startTime = Date.now();
        // Convert file to buffer
        const arrayBuffer = await file.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        // Parse PDF using custom parser
        const pdfData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$pdfParser$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePDF"])(buffer);
        // Extract structured data
        const extractedMetadata = extractMetadata(pdfData, file);
        const content = extractContent(pdfData);
        const statistics = calculateStatistics(pdfData, content, file, startTime);
        // Perform AI analysis
        const aiAnalysis = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$aiAnalyzer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AIAnalyzer"].analyzeDocument(content.fullText, file.name);
        const result = {
            metadata: extractedMetadata,
            content,
            statistics,
            aiAnalysis,
            fileName: file.name,
            extractedAt: new Date().toISOString()
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('PDF extraction error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to extract PDF data. Please ensure the file is a valid PDF.'
        }, {
            status: 500
        });
    }
}
function extractMetadata(pdfData, file) {
    const info = pdfData.info || {};
    return {
        title: info.Title || 'Unknown',
        author: info.Author || 'Unknown',
        subject: info.Subject || 'Not specified',
        creator: info.Creator || 'Unknown',
        producer: info.Producer || 'Unknown',
        creationDate: info.CreationDate ? new Date(info.CreationDate).toLocaleDateString() : 'Unknown',
        modificationDate: info.ModDate ? new Date(info.ModDate).toLocaleDateString() : 'Unknown',
        keywords: info.Keywords || 'None',
        pages: pdfData.numpages || 0
    };
}
function extractContent(pdfData) {
    const fullText = pdfData.text || '';
    const lines = fullText.split('\n').filter((line)=>line.trim());
    // Simple structure extraction
    const headings = lines.filter((line)=>line.length < 100 && (line.match(/^[A-Z\s]+$/) || line.match(/^\d+\.?\s+[A-Z]/)));
    const paragraphs = lines.filter((line)=>line.length > 50 && !headings.includes(line));
    // Extract potential tables (lines with multiple spaces or tabs)
    const tables = lines.filter((line)=>line.includes('\t') || line.match(/\s{3,}/));
    // Extract lists (lines starting with bullets or numbers)
    const lists = lines.filter((line)=>line.match(/^[\s]*[-•*]\s/) || line.match(/^[\s]*\d+[\.)]\s/));
    // Split text by pages (approximate)
    const pageTexts = splitTextByPages(fullText, pdfData.numpages);
    return {
        fullText,
        pageTexts,
        structure: {
            headings: headings.slice(0, 20),
            paragraphs: paragraphs.slice(0, 10),
            tables: tables.slice(0, 10),
            lists: lists.slice(0, 15)
        }
    };
}
function splitTextByPages(text, numPages) {
    if (numPages <= 1) return [
        text
    ];
    const lines = text.split('\n');
    const linesPerPage = Math.ceil(lines.length / numPages);
    const pages = [];
    for(let i = 0; i < numPages; i++){
        const start = i * linesPerPage;
        const end = Math.min((i + 1) * linesPerPage, lines.length);
        pages.push(lines.slice(start, end).join('\n'));
    }
    return pages;
}
function calculateStatistics(pdfData, content, file, startTime) {
    const totalCharacters = content.fullText.length;
    const totalWords = content.fullText.split(/\s+/).filter((word)=>word.length > 0).length;
    const totalLines = content.fullText.split('\n').length;
    const totalPages = pdfData.numpages || 1;
    const processingTime = Date.now() - startTime;
    return {
        totalPages,
        totalCharacters,
        totalWords,
        totalLines,
        averageWordsPerPage: Math.round(totalWords / totalPages),
        fileSize: formatFileSize(file.size),
        processingTime
    };
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = [
        'Bytes',
        'KB',
        'MB',
        'GB'
    ];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__3da2bc54._.js.map